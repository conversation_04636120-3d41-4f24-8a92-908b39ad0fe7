import { useState, useEffect, useCallback } from "react";
import { MemberWrapper } from "../../../components/MemberWrapper";
import { Link, useNavigate } from "react-router-dom";
import { PayCreditBalanceModal } from "../../../components/PayCreditBalanceModal";
import DashboardCardsGrid from "../../../components/DashboardCardsGrid";
import RecentTransactionsTable from "../../../components/RecentTransactionsTable";
import { useSDK } from "../../../hooks/useSDK";
import { useToast } from "../../../hooks/useToast";

// Interface definitions
interface DashboardData {
  user: {
    id: number;
    name: string;
    email: string;
  };
  account_balance: {
    current_balance: number;
    currency: string;
    monthly_change: number;
    last_topped_up: string | null;
  };
  credit_line: {
    limit: number;
    used: number;
    available: number;
    utilization_percentage: number;
    balance_due: number;
    due_date: string | null;
  };
  rating: {
    average_rating: number;
    total_ratings: number;
  };
  commissions: {
    total_amount: number;
    active_referrals: number;
  };
  rewards: {
    total_amount: number;
    monthly_amount: number;
  };
  onboarding: {
    id: number;
    user_id: number;
    account_created: boolean;
    email_verified: boolean;
    first_listing_created: boolean;
    first_purchase_made: boolean;
    profile_completed: boolean;
    progress_percentage: number;
  } | null;
  recent_transactions: Transaction[];
}

interface Transaction {
  id: number;
  date: string;
  description: string;
  type: string;
  amount: number;
  status: string;
}

const MemberDashboardPage = () => {
  const { sdk } = useSDK();
  const { success, error: showError } = useToast();
  const navigate = useNavigate();

  // State management
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showGetStarted, setShowGetStarted] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [paymentProcessing, setPaymentProcessing] = useState(false);

  const loadDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/dashboard/summary",
        method: "GET",
      });

      console.log("Dashboard API Response:", response);

      if (!response.error) {
        // Process and validate the data
        const processedData = {
          user: {
            id: Number(response.data?.user?.id || 0),
            name: String(response.data?.user?.name || ""),
            email: String(response.data?.user?.email || ""),
          },
          account_balance: {
            current_balance: Number(
              response.data?.account_balance?.current_balance || 0
            ),
            currency: String(
              response.data?.account_balance?.currency || "eBa$"
            ),
            monthly_change: Number(
              response.data?.account_balance?.monthly_change || 0
            ),
            last_topped_up:
              response.data?.account_balance?.last_topped_up || null,
          },
          credit_line: {
            limit: Number(response.data?.credit_line?.limit || 0),
            used: Number(response.data?.credit_line?.used || 0),
            available: Number(response.data?.credit_line?.available || 0),
            utilization_percentage: Number(
              response.data?.credit_line?.utilization_percentage || 0
            ),
            balance_due: Number(response.data?.credit_line?.balance_due || 0),
            due_date: response.data?.credit_line?.due_date || null,
          },
          rating: {
            average_rating: Number(response.data?.rating?.average_rating || 0),
            total_ratings: Number(response.data?.rating?.total_ratings || 0),
          },
          commissions: {
            total_amount: Number(response.data?.commissions?.total_amount || 0),
            active_referrals: Number(
              response.data?.commissions?.active_referrals || 0
            ),
          },
          rewards: {
            total_amount: Number(response.data?.rewards?.total_amount || 0),
            monthly_amount: Number(response.data?.rewards?.monthly_amount || 0),
          },
          onboarding: response.data?.onboarding
            ? {
                id: Number(response.data.onboarding.id || 0),
                user_id: Number(response.data.onboarding.user_id || 0),
                account_created: Boolean(
                  response.data.onboarding.account_created
                ),
                email_verified: Boolean(
                  response.data.onboarding.email_verified
                ),
                first_listing_created: Boolean(
                  response.data.onboarding.first_listing_created
                ),
                first_purchase_made: Boolean(
                  response.data.onboarding.first_purchase_made
                ),
                profile_completed: Boolean(
                  response.data.onboarding.profile_completed
                ),
                progress_percentage: Number(
                  response.data.onboarding.progress_percentage || 0
                ),
              }
            : null,
          recent_transactions: Array.isArray(response.data?.recent_transactions)
            ? response.data.recent_transactions.map((t: any) => ({
                id: Number(t.id || 0),
                date: String(t.date || ""),
                description: String(t.description || ""),
                type: String(t.type || ""),
                amount: Number(t.amount || 0),
                status: String(t.status || ""),
              }))
            : [],
        };

        setDashboardData(processedData);

        // Determine if user should see welcome dashboard
        // User should see the full dashboard only when:
        // 1. Progress percentage is 100% AND
        // 2. User has both listed a product AND made a purchase
        const hasCompletedOnboarding = Boolean(
          processedData.onboarding &&
            processedData.onboarding.progress_percentage >= 100
        );

        const hasListedAndPurchased = Boolean(
          processedData.onboarding &&
            processedData.onboarding.first_listing_created &&
            processedData.onboarding.first_purchase_made
        );

        // Show Get Started screen unless BOTH conditions are met
        const shouldShowFullDashboard =
          hasCompletedOnboarding && hasListedAndPurchased;
        setShowGetStarted(!shouldShowFullDashboard);
      } else {
        setError(String(response.message || "Failed to load dashboard data"));
      }
    } catch (error: any) {
      console.error("Error loading dashboard data:", error);
      setError(String(error?.message || "An unexpected error occurred"));
    } finally {
      setLoading(false);
    }
  }, []);

  // Load dashboard data on component mount
  useEffect(() => {
    loadDashboardData();
  }, []);

  const handleCreditPayment = async (paymentAmount: number) => {
    try {
      setPaymentProcessing(true);

      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/dashboard/credit-payment",
        method: "POST",
        body: { payment_amount: paymentAmount },
      });

      console.log("Credit Payment API Response:", response);

      if (!response.error) {
        success("Payment processed successfully");
        setIsModalOpen(false);
        // Reload dashboard data to reflect changes
        loadDashboardData();
      } else {
        showError(String(response.message || "Payment failed"));
      }
    } catch (error: any) {
      console.error("Error processing payment:", error);
      showError(String(error?.message || "Payment failed"));
    } finally {
      setPaymentProcessing(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return `eBa$${amount.toFixed(2)}`;
  };

  const formatTransactionAmount = (amount: number) => {
    const prefix = amount >= 0 ? "+" : "";
    return `${prefix}${formatCurrency(amount)}`;
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(
        <span key={i} className="text-yellow-400">
          ★
        </span>
      );
    }

    if (hasHalfStar) {
      stars.push(
        <span key="half" className="text-yellow-400">
          ☆
        </span>
      );
    }

    const remainingStars = 5 - Math.ceil(rating);
    for (let i = 0; i < remainingStars; i++) {
      stars.push(
        <span key={`empty-${i}`} className="text-gray-300">
          ☆
        </span>
      );
    }

    return stars;
  };

  // Show loading state
  if (loading) {
    return (
      <MemberWrapper>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#E63946] mx-auto mb-4"></div>
            <p className="text-gray-600">Loading dashboard...</p>
          </div>
        </div>
      </MemberWrapper>
    );
  }

  // Show error state if there's an error or no data
  if (error || (!loading && !dashboardData)) {
    return (
      <MemberWrapper>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="text-4xl mb-4">⚠️</div>
            <p className="text-gray-600 mb-4">
              {error || "Failed to load dashboard data"}
            </p>
            <button
              onClick={loadDashboardData}
              className="px-4 py-2 bg-[#E63946] text-white rounded hover:bg-opacity-90"
            >
              Retry
            </button>
          </div>
        </div>
      </MemberWrapper>
    );
  }

  if (showGetStarted && dashboardData) {
    const userName = dashboardData.user?.name || "User";

    // Calculate completed steps for progress display
    const steps = [
      dashboardData.onboarding?.account_created,
      dashboardData.onboarding?.email_verified,
      dashboardData.onboarding?.first_listing_created,
      dashboardData.onboarding?.first_purchase_made,
      dashboardData.onboarding?.profile_completed,
    ];
    const completedCount = steps.filter(Boolean).length;
    const totalSteps = steps.length;
    const stepProgressPercentage = (completedCount / totalSteps) * 100;

    return (
      <MemberWrapper>
        <div className="h-full bg-[#0F2C59] overflow-auto">
          <div className="p-8">
            {/* Welcome Section */}
            <div className="mb-8">
              <h1 className="text-4xl font-bold text-white mb-2">
                Welcome, {userName}!
              </h1>
              <p className="text-white text-lg">
                Let's get you started on the eBaDollar Platform
              </p>
            </div>

            {/* Get Started Section */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-white">Get Started</h2>
                <span className="text-white text-lg">
                  {completedCount} of {totalSteps} completed
                </span>
              </div>

              {/* Progress Bar */}
              <div className="mb-6">
                <div className="w-full bg-gray-600 rounded-full h-2">
                  <div
                    className="bg-[#E63946] h-2 rounded-full transition-all duration-500"
                    style={{ width: `${stepProgressPercentage}%` }}
                  ></div>
                </div>
              </div>

              <div className="grid grid-cols-5 gap-4">
                {/* Step 1 - Create Account */}
                <div className="bg-white rounded-xl p-6 text-center">
                  <div
                    className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
                      true ? "bg-[#E63946]" : "bg-gray-400"
                    }`}
                  >
                    {true ? (
                      <svg
                        className="w-8 h-8 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    ) : (
                      <svg
                        className="w-8 h-8 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                  </div>
                  <h3 className="font-bold text-[#0F2C59] text-sm">
                    Create Account
                  </h3>
                </div>

                {/* Step 2 - Verify Email */}
                <div className="bg-white rounded-xl p-6 text-center">
                  <div
                    className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
                      true ? "bg-[#E63946]" : "bg-gray-400"
                    }`}
                  >
                    {true ? (
                      <svg
                        className="w-8 h-8 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    ) : (
                      <svg
                        className="w-8 h-8 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                      </svg>
                    )}
                  </div>
                  <h3 className="font-bold text-[#0F2C59] text-sm">
                    Verify Email
                  </h3>
                </div>

                {/* Step 3 - List First Product */}
                <div
                  className={`bg-white rounded-xl p-6 text-center ${
                    !dashboardData.onboarding?.first_listing_created
                      ? "cursor-pointer hover:bg-gray-50 transition-colors"
                      : ""
                  }`}
                  onClick={() => {
                    if (!dashboardData.onboarding?.first_listing_created) {
                      navigate("/member/listings/add");
                    }
                  }}
                >
                  <div
                    className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
                      dashboardData.onboarding?.first_listing_created
                        ? "bg-[#E63946]"
                        : "bg-gray-400"
                    }`}
                  >
                    {dashboardData.onboarding?.first_listing_created ? (
                      <svg
                        className="w-8 h-8 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    ) : (
                      <svg
                        className="w-8 h-8 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                        <path
                          fillRule="evenodd"
                          d="M4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 1a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                  </div>
                  <h3 className="font-bold text-[#0F2C59] text-sm">
                    List First Product
                  </h3>
                </div>

                {/* Step 4 - Make First Purchase */}
                <div
                  className={`bg-white rounded-xl p-6 text-center ${
                    !dashboardData.onboarding?.first_purchase_made
                      ? "cursor-pointer hover:bg-gray-50 transition-colors"
                      : ""
                  }`}
                  onClick={() => {
                    if (!dashboardData.onboarding?.first_purchase_made) {
                      navigate("/member/marketplace");
                    }
                  }}
                >
                  <div
                    className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
                      dashboardData.onboarding?.first_purchase_made
                        ? "bg-[#E63946]"
                        : "bg-gray-400"
                    }`}
                  >
                    {dashboardData.onboarding?.first_purchase_made ? (
                      <svg
                        className="w-8 h-8 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    ) : (
                      <svg
                        className="w-8 h-8 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 3H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                      </svg>
                    )}
                  </div>
                  <h3 className="font-bold text-[#0F2C59] text-sm">
                    Make First Purchase
                  </h3>
                </div>

                {/* Step 5 - Complete Profile */}
                <div
                  className={`bg-white rounded-xl p-6 text-center ${
                    !dashboardData.onboarding?.profile_completed
                      ? "cursor-pointer hover:bg-gray-50 transition-colors"
                      : ""
                  }`}
                  onClick={() => {
                    if (!dashboardData.onboarding?.profile_completed) {
                      navigate("/member/verify-identity");
                    }
                  }}
                >
                  <div
                    className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${
                      dashboardData.onboarding?.profile_completed
                        ? "bg-[#E63946]"
                        : "bg-gray-400"
                    }`}
                  >
                    {dashboardData.onboarding?.profile_completed ? (
                      <svg
                        className="w-8 h-8 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    ) : (
                      <svg
                        className="w-8 h-8 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                  </div>
                  <h3 className="font-bold text-[#0F2C59] text-sm">
                    Complete Profile
                  </h3>
                  <p className="text-gray-500 text-xs mt-1">(Optional)</p>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="mb-8">
              <h2 className="text-xl font-bold text-white mb-4">
                Quick Actions
              </h2>

              <div className="grid grid-cols-3 gap-4">
                {/* Create a Listing */}
                <div className="bg-white rounded-lg p-6">
                  <div className="w-12 h-12 bg-[#E63946] rounded-lg flex items-center justify-center mb-4">
                    <svg
                      className="w-6 h-6 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <h3 className="font-bold text-[#0F2C59] mb-2">
                    Create a Listing
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Add your products and services to sell on eBaDollar and
                    start buying and selling immediately!
                  </p>
                  <Link
                    to="/member/listings/add"
                    className="inline-block bg-[#E63946] text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-[#d42620] transition-colors"
                  >
                    List Now →
                  </Link>
                </div>

                {/* View Marketplace */}
                <div className="bg-white rounded-lg p-6">
                  <div className="w-12 h-12 bg-[#E63946] rounded-lg flex items-center justify-center mb-4">
                    <svg
                      className="w-6 h-6 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                    </svg>
                  </div>
                  <h3 className="font-bold text-[#0F2C59] mb-2">
                    View Marketplace
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    Browse products and services for sale on the eBaDollar
                    marketplace.
                  </p>
                  <Link
                    to="/member/marketplace"
                    className="inline-block bg-[#E63946] text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-[#d42620] transition-colors"
                  >
                    Shop Now →
                  </Link>
                </div>

                {/* Check Rewards */}
                <div className="bg-white rounded-lg p-6">
                  <div className="w-12 h-12 bg-[#E63946] rounded-lg flex items-center justify-center mb-4">
                    <svg
                      className="w-6 h-6 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <h3 className="font-bold text-[#0F2C59] mb-2">
                    Check Rewards
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    View how you can earn rewards and commissions on eBaDollar.
                  </p>
                  <Link
                    to="/member/rewards"
                    className="inline-block bg-[#E63946] text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-[#d42620] transition-colors"
                  >
                    View Rewards →
                  </Link>
                </div>
              </div>
            </div>
            {/* Top Cards */}
            <DashboardCardsGrid
              dashboardData={dashboardData}
              formatCurrency={formatCurrency}
              formatTransactionAmount={formatTransactionAmount}
              renderStars={renderStars}
              onPayBalance={() => setIsModalOpen(true)}
              paymentProcessing={paymentProcessing}
            />

            {/* Recent Transactions */}
            <RecentTransactionsTable
              transactions={dashboardData.recent_transactions}
              formatTransactionAmount={formatTransactionAmount}
            />

            {/* Helpful Tips Section */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-white mb-6">
                Helpful Tips
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Card 1: How to use your credit line */}
                <div className="bg-white rounded-2xl p-6 shadow-lg">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-6 bg-red-500 rounded flex items-center justify-center mr-3">
                      <div className="w-8 h-1 bg-white rounded"></div>
                    </div>
                  </div>
                  <h3 className="text-red-500 font-bold text-lg mb-2">
                    How to use your credit line
                  </h3>
                  <p className="text-gray-800 text-sm mb-4">
                    Use your eBaCredit to buy whatever you want immediately,
                    even before you make a sale!
                  </p>
                  <a href="#" className="text-red-500 text-sm font-medium">
                    Learn More
                  </a>
                </div>

                {/* Card 2: How to make your first sale */}
                <div className="bg-white rounded-2xl p-6 shadow-lg">
                  <div className="flex items-center mb-4">
                    <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center mr-3">
                      <svg
                        className="w-4 h-4 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                  <h3 className="text-red-500 font-bold text-lg mb-2">
                    How to make your first sale
                  </h3>
                  <p className="text-gray-800 text-sm mb-4">
                    Learn how to sell your products and services quickly to earn
                    eBaDollars faster!
                  </p>
                  <a href="#" className="text-red-500 text-sm font-medium">
                    Learn More
                  </a>
                </div>

                {/* Card 3: Invite friends & earn bonuses */}
                <div className="bg-white rounded-2xl p-6 shadow-lg">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-6 bg-red-500 rounded flex items-center justify-center mr-3">
                      <svg
                        className="w-6 h-6 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                      </svg>
                    </div>
                  </div>
                  <h3 className="text-red-500 font-bold text-lg mb-2">
                    Invite friends & earn bonuses
                  </h3>
                  <p className="text-gray-800 text-sm mb-4">
                    Invite your contacts to join and earn bonuses and
                    commissions!
                  </p>
                  <a href="#" className="text-red-500 text-sm font-medium">
                    Learn More
                  </a>
                </div>
              </div>
            </div>

            {/* User Profile Section */}
            <div className="mb-8">
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="flex items-center">
                  {/* Profile Picture */}
                  <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center mr-4">
                    <svg
                      className="w-8 h-8 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>

                  {/* User Details */}
                  <div className="flex-1">
                    <h3 className="text-red-500 font-bold text-xl">
                      {dashboardData.user?.name || "Alex Johnson"}
                    </h3>
                    <p className="text-gray-600 text-sm">
                      Member since May 16, 2025
                    </p>
                  </div>

                  {/* Complete Profile Button */}
                  <button onClick={()=>navigate("/member/profile")} className="bg-white border border-red-500 text-red-500 px-4 py-2 rounded-lg flex items-center hover:bg-red-50 transition-colors">
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Complete Your Profile
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </MemberWrapper>
    );
  }

  // Ensure dashboardData is available before rendering
  if (!dashboardData) {
    return null; // This should not happen due to earlier checks, but TypeScript safety
  }

  return (
    <MemberWrapper>
      <div className="h-full bg-[#0A2540] overflow-auto text-white p-6">
        <h1 className="text-3xl font-bold mb-6">Dashboard</h1>

        {/* Top Cards */}
        <DashboardCardsGrid
          dashboardData={dashboardData}
          formatCurrency={formatCurrency}
          formatTransactionAmount={formatTransactionAmount}
          renderStars={renderStars}
          onPayBalance={() => setIsModalOpen(true)}
          paymentProcessing={paymentProcessing}
        />

        {/* Recent Transactions */}
        <RecentTransactionsTable
          transactions={dashboardData.recent_transactions}
          formatTransactionAmount={formatTransactionAmount}
        />

        {/* Quick Actions */}
        <div>
          <h2 className="text-xl font-bold text-white mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white p-6 rounded-lg shadow text-gray-800 text-center">
              <div className="text-4xl text-red-500 mb-2">🏷️</div>
              <h3 className="font-bold mb-2">Create a Listing</h3>
              <p className="text-sm text-gray-600 mb-4">
                Add your products and services to earn eBaCredit and start
                buying and selling immediately!
              </p>
              <Link
                to="/member/listings/add"
                className="bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600"
              >
                List Now →
              </Link>
            </div>
            <div className="bg-white p-6 rounded-lg shadow text-gray-800 text-center">
              <div className="text-4xl text-red-500 mb-2">🛒</div>
              <h3 className="font-bold mb-2">View Marketplace</h3>
              <p className="text-sm text-gray-600 mb-4">
                See what's on sale now!
              </p>
              <Link
                to="/member/marketplace"
                className="bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600"
              >
                Shop Now →
              </Link>
            </div>
            <div className="bg-white p-6 rounded-lg shadow text-gray-800 text-center">
              <div className="text-4xl text-red-500 mb-2">🎁</div>
              <h3 className="font-bold mb-2">Check Rewards</h3>
              <p className="text-sm text-gray-600 mb-4">
                See how to earn rewards!
              </p>
              <Link
                to="/member/rewards"
                className="bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600"
              >
                View Rewards →
              </Link>
            </div>
          </div>
        </div>
      </div>
      <PayCreditBalanceModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        outstandingBalance={dashboardData.credit_line.balance_due}
        onPayment={handleCreditPayment}
        processing={paymentProcessing}
      />
    </MemberWrapper>
  );
};

export default MemberDashboardPage;
