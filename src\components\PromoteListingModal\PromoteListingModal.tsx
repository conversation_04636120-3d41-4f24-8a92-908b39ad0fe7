import React from "react";
import { Modal } from "@/components/Modal";
import InteractiveButton from "@/components/InteractiveButton/InteractiveButton";
import { MkdInputV2 } from "@/components/MkdInputV2";

interface PromoteListingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (days: number) => void;
}

const PromoteListingModal: React.FC<PromoteListingModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
}) => {
  const [days, setDays] = React.useState(1);

  const handleConfirm = () => {
    onConfirm(days);
  };

  if (!isOpen) {
    return null;
  }

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={onClose}
      title=""
      modalHeader={false}
      classes={{
        modal: "flex items-center justify-center",
        modalDialog:
          "bg-white rounded-lg shadow-xl p-8 w-full max-w-lg mx-auto text-black",
      }}
    >
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <svg
              className="w-6 h-6 text-[#0D3166]"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z"
              />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-800">
            Promote Your Listing
          </h2>
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 text-2xl font-bold"
        >
          &times;
        </button>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          For how many days do you want to promote this listing?
        </label>
        <MkdInputV2
          type="number"
          placeholder="Enter number of days"
          value={String(days)}
          onChange={(e) => setDays(Number(e.target.value))}
        >
          <MkdInputV2.Field className="!border-gray-300 !bg-white !text-black !placeholder-gray-400 !focus:ring-[#0D3166]" />
        </MkdInputV2>
      </div>
      <div className="mt-6 flex justify-between items-center">
        <span className="text-sm font-medium text-gray-700">
          Promotion Rate
        </span>
        <span className="text-lg font-bold text-gray-800">USD$ —</span>
      </div>
      <div className="mt-8 flex justify-end gap-4">
        <InteractiveButton
          onClick={onClose}
          className="border border-gray-300 text-gray-800 px-6 py-2 rounded-md hover:bg-gray-100 font-semibold"
        >
          Cancel
        </InteractiveButton>
        <InteractiveButton
          onClick={handleConfirm}
          className="bg-[#0D3166] text-white px-6 py-2 rounded-md hover:bg-blue-800 font-semibold"
        >
          Confirm Promotion
        </InteractiveButton>
      </div>
    </Modal>
  );
};

export default PromoteListingModal;
