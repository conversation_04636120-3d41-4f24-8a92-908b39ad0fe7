import React from 'react';

const FileMemoIcon = ({ className = "", fill = "#A8A8A8", onClick = () => { } }) => {
    return (
        <svg onClick={onClick} className={`${className}`} xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path fillRule="evenodd" clipRule="evenodd" d="M3.33331 3.12508C3.33331 2.31967 3.98623 1.66675 4.79165 1.66675H15.2083C16.0137 1.66675 16.6666 2.31967 16.6666 3.12508V16.8751C16.6666 17.6805 16.0137 18.3334 15.2083 18.3334H4.79165C3.98623 18.3334 3.33331 17.6805 3.33331 16.8751V3.12508ZM7.29165 5.00008C6.94647 5.00008 6.66665 5.2799 6.66665 5.62508C6.66665 5.97026 6.94647 6.25008 7.29165 6.25008H12.7083C13.0535 6.25008 13.3333 5.97026 13.3333 5.62508C13.3333 5.2799 13.0535 5.00008 12.7083 5.00008H7.29165ZM7.29165 8.33342C6.94647 8.33342 6.66665 8.61324 6.66665 8.95842C6.66665 9.30359 6.94647 9.58342 7.29165 9.58342H12.7083C13.0535 9.58342 13.3333 9.30359 13.3333 8.95842C13.3333 8.61324 13.0535 8.33342 12.7083 8.33342H7.29165ZM7.29165 11.6667C6.94647 11.6667 6.66665 11.9466 6.66665 12.2917C6.66665 12.6369 6.94647 12.9167 7.29165 12.9167H9.37498C9.72016 12.9167 9.99998 12.6369 9.99998 12.2917C9.99998 11.9466 9.72016 11.6667 9.37498 11.6667H7.29165Z" fill={fill} />
        </svg>
    );
};

export default FileMemoIcon;