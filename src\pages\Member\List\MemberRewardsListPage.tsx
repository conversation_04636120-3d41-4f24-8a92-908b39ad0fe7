import React, { useState } from "react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  CartesianGrid,
} from "recharts";
import { MemberWrapper } from "../../../components/MemberWrapper";
import {
  ArrowTrendingUpIcon,
  Cog6ToothIcon,
  CreditCardIcon,
  DocumentDuplicateIcon,
  StarIcon,
  UsersIcon,
  ShoppingCartIcon,
  CalculatorIcon,
  ArrowDownTrayIcon,
  ChevronDownIcon,
} from "@heroicons/react/24/outline";
import { MkdInputV2 } from "@/components/MkdInputV2";
import { Skeleton } from "../../../components/Skeleton";
import {
  useRewardsOverviewQuery,
  usePerformanceMetricsQuery,
  useRewardsChartDataQuery,
  useReferralDataQuery,
  useMonthlyBreakdownQuery,
  useShareReferralMutation,
  useInviteByEmailMutation,
} from "../../../query/useRewards";

const StatCard = ({
  title,
  value,
  subtext,
}: {
  title: string;
  value: string;
  subtext: string;
}) => (
  <div>
    <p className="text-sm text-gray-500">{title}</p>
    <p className="text-2xl font-bold text-[#0F2C59]">{value}</p>
    <p className="text-xs text-gray-400">{subtext}</p>
  </div>
);

const chartData = {
  loyalty: [
    { name: "Nov", value: 250 },
    { name: "Dec", value: 350 },
    { name: "Jan", value: 450 },
    { name: "Feb", value: 300 },
    { name: "Mar", value: 370 },
    { name: "Apr", value: 420 },
  ],
  referral: [
    { name: "Nov", value: 280 },
    { name: "Dec", value: 220 },
    { name: "Jan", value: 400 },
    { name: "Feb", value: 320 },
    { name: "Mar", avalue: 430 },
    { name: "Apr", value: 400 },
  ],
  rewardsRedeemed: [
    { name: "Nov", value: 150 },
    { name: "Dec", value: 250 },
    { name: "Jan", value: 350 },
    { name: "Feb", value: 200 },
    { name: "Mar", value: 270 },
    { name: "Apr", value: 320 },
  ],
  commissionsRedeemed: [
    { name: "Nov", value: 200 },
    { name: "Dec", value: 180 },
    { name: "Jan", value: 300 },
    { name: "Feb", value: 240 },
    { name: "Mar", value: 330 },
    { name: "Apr", value: 300 },
  ],
};

const BarChartCard = ({
  title,
  data,
  color,
}: {
  title: string;
  data: any[];
  color: string;
}) => (
  <div className="bg-white p-4 rounded-lg shadow">
    <div className="flex justify-between items-center mb-4">
      <h3 className="font-semibold text-[#0F2C59]">{title}</h3>
      <p className="text-xs text-gray-400">Last 6 months</p>
    </div>
    <div className="h-48">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 5, right: 0, left: 0, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis
            dataKey="name"
            axisLine={false}
            tickLine={false}
            tick={{ fill: "#6B7280", fontSize: 12 }}
          />
          <YAxis hide={true} />
          <Tooltip
            cursor={{ fill: "rgba(15, 44, 89, 0.1)" }}
            contentStyle={{
              background: "#0F2C59",
              border: "none",
              borderRadius: "0.5rem",
              color: "white",
            }}
          />
          <Bar
            dataKey="value"
            fill={color}
            barSize={20}
            radius={[4, 4, 0, 0]}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  </div>
);

const MemberRewardsListPage = () => {
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [emailInviteList, setEmailInviteList] = useState<string[]>([]);
  const [emailInput, setEmailInput] = useState("");

  // Query hooks
  const {
    data: rewardsOverviewData,
    isLoading: rewardsOverviewLoading,
    error: rewardsOverviewError,
  } = useRewardsOverviewQuery();

  const {
    data: performanceData,
    isLoading: performanceLoading,
    error: performanceError,
  } = usePerformanceMetricsQuery();

  const {
    data: chartData,
    isLoading: chartLoading,
    error: chartError,
  } = useRewardsChartDataQuery();

  const {
    data: referralData,
    isLoading: referralLoading,
    error: referralError,
  } = useReferralDataQuery();

  const {
    data: monthlyBreakdownData,
    isLoading: monthlyBreakdownLoading,
    error: monthlyBreakdownError,
  } = useMonthlyBreakdownQuery(selectedYear);

  const { mutate: shareReferral, isPending: isSharing } =
    useShareReferralMutation();
  const { mutate: inviteByEmail, isPending: isInviting } =
    useInviteByEmailMutation();

  // Get data from queries
  const loyaltyData = rewardsOverviewData?.data;
  const performanceMetrics = performanceData?.data;
  const referralInfo = referralData?.data;
  const monthlyBreakdown = monthlyBreakdownData?.data || [];
  const chartDataInfo = (chartData as any)?.data || {};

  // Event handlers
  const handleShareLink = () => {
    shareReferral({
      method: "social",
      recipients: ["social_media"],
    });
  };

  const handleInviteByEmail = () => {
    if (emailInviteList.length > 0) {
      inviteByEmail({
        emails: emailInviteList,
        message: "Join me on EbaDollar and start earning rewards!",
      });
      setEmailInviteList([]);
      setEmailInput("");
    }
  };

  const handleAddEmail = () => {
    if (emailInput && !emailInviteList.includes(emailInput)) {
      setEmailInviteList([...emailInviteList, emailInput]);
      setEmailInput("");
    }
  };

  const handleCopyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  return (
    <MemberWrapper>
      <div className="p-8 bg-[#F0F4F8] text-[#0F2C59]">
        <h1 className="text-4xl font-bold mb-8">Rewards Center</h1>

        {/* Loyalty Rewards Overview */}
        <div className="bg-white p-6 rounded-lg shadow mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Loyalty Rewards Overview</h2>
            <Cog6ToothIcon className="h-6 w-6 text-gray-400" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {rewardsOverviewLoading ? (
              // Loading skeleton
              [...Array(4)].map((_, index) => (
                <div key={index}>
                  <Skeleton className="h-4 w-32 mb-2" />
                  <Skeleton className="h-8 w-24 mb-1" />
                  <Skeleton className="h-3 w-20" />
                </div>
              ))
            ) : rewardsOverviewError ? (
              <div className="col-span-4 text-center py-4">
                <p className="text-red-500">Failed to load rewards overview</p>
              </div>
            ) : loyaltyData ? (
              <>
                <StatCard
                  title="Total Loyalty Rewards"
                  value={`eBa$ ${loyaltyData.totalLoyaltyRewards}`}
                  subtext="Lifetime earnings"
                />
                <StatCard
                  title="This Month's Rewards"
                  value={`eBa$ ${loyaltyData.thisMonthRewards}`}
                  subtext={new Date().toLocaleDateString("en-US", {
                    month: "long",
                    year: "numeric",
                  })}
                />
                <StatCard
                  title="Redeemed This Month"
                  value={`eBa$ ${loyaltyData.redeemedThisMonth}`}
                  subtext={new Date().toLocaleDateString("en-US", {
                    month: "long",
                    year: "numeric",
                  })}
                />
                <StatCard
                  title="Available Balance"
                  value={`eBa$ ${loyaltyData.availableBalance}`}
                  subtext="After redeemed rewards"
                />
              </>
            ) : (
              <div className="col-span-4 text-center py-4">
                <p className="text-gray-500">No rewards data available</p>
              </div>
            )}
          </div>
        </div>

        {/* Rewards Calculator Preview */}
        <div className="bg-white p-6 rounded-lg shadow mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">
              Rewards Calculator Preview
            </h2>
            <DocumentDuplicateIcon className="h-6 w-6 text-gray-400" />
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <h3 className="font-semibold mb-4">Performance Metrics</h3>
              {performanceLoading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  {[...Array(4)].map((_, index) => (
                    <div
                      key={index}
                      className="p-4 rounded-lg border border-gray-200"
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <Skeleton className="h-4 w-24 mb-2" />
                          <Skeleton className="h-8 w-20 mb-2" />
                          <Skeleton className="h-3 w-32" />
                        </div>
                        <Skeleton className="h-6 w-6" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : performanceError ? (
                <div className="text-center py-4">
                  <p className="text-red-500">
                    Failed to load performance metrics
                  </p>
                </div>
              ) : performanceMetrics ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  {/* Last Month Sales */}
                  <div className="p-4 rounded-lg border border-gray-200">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm text-gray-500">
                          Last Month Sales
                        </p>
                        <p className="text-2xl font-bold text-[#0F2C59] my-1">
                          eBa$ {performanceMetrics.lastMonthSales}
                        </p>
                        <p className="text-xs font-medium text-green-500 flex items-center">
                          <ArrowTrendingUpIcon className="h-4 w-4 mr-1" />+
                          {performanceMetrics.salesGrowth}% from previous month
                        </p>
                      </div>
                      <ArrowTrendingUpIcon className="h-6 w-6 text-green-400" />
                    </div>
                  </div>

                  {/* Last Month Purchases */}
                  <div className="p-4 rounded-lg border border-gray-200">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm text-gray-500">
                          Last Month Purchases
                        </p>
                        <p className="text-2xl font-bold text-[#0F2C59] my-1">
                          eBa$ {performanceMetrics.lastMonthPurchases}
                        </p>
                        <p className="text-xs font-medium text-green-500 flex items-center">
                          <ArrowTrendingUpIcon className="h-4 w-4 mr-1" />+
                          {performanceMetrics.purchasesGrowth}% from previous
                          month
                        </p>
                      </div>
                      <ShoppingCartIcon className="h-6 w-6 text-blue-400" />
                    </div>
                  </div>

                  {/* Credit Score */}
                  <div className="p-4 rounded-lg border border-gray-200">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm text-gray-500">Credit Score</p>
                        <p className="text-2xl font-bold text-[#0F2C59] my-1">
                          {performanceMetrics.creditScore}
                        </p>
                        <span
                          className={`text-xs font-medium px-2 py-0.5 rounded-full ${
                            performanceMetrics.creditScore >= 750
                              ? "bg-green-100 text-green-700"
                              : performanceMetrics.creditScore >= 700
                                ? "bg-purple-100 text-purple-700"
                                : "bg-yellow-100 text-yellow-700"
                          }`}
                        >
                          {performanceMetrics.creditScore >= 750
                            ? "Excellent"
                            : performanceMetrics.creditScore >= 700
                              ? "Good"
                              : "Fair"}
                        </span>
                      </div>
                      <CreditCardIcon className="h-6 w-6 text-purple-400" />
                    </div>
                  </div>

                  {/* Star Rating */}
                  <div className="p-4 rounded-lg border border-gray-200">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm text-gray-500">Star Rating</p>
                        <div className="flex items-center gap-2">
                          <p className="text-2xl font-bold text-[#0F2C59] my-1">
                            {performanceMetrics.starRating}
                          </p>
                          <div className="flex text-yellow-400">
                            {[...Array(5)].map((_, i) => (
                              <StarIcon
                                key={i}
                                className={`h-5 w-5 ${
                                  i <
                                  Math.floor(
                                    parseFloat(performanceMetrics.starRating)
                                  )
                                    ? "fill-current"
                                    : "stroke-current"
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                        <p className="text-xs text-gray-400">
                          Based on {performanceMetrics.reviewCount} reviews
                        </p>
                      </div>
                      <StarIcon className="h-6 w-6 text-yellow-400" />
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-gray-500">No performance data available</p>
                </div>
              )}
            </div>

            {/* Reward Calculation */}
            <div>
              <h3 className="font-semibold mb-4">Reward Calculation</h3>
              <div className="bg-[#0F2C59] text-white p-6 rounded-lg flex flex-col items-center justify-center text-center h-full">
                <CalculatorIcon className="h-10 w-10 mb-4 text-yellow-400" />
                <p className="font-semibold text-white/90">Monthly Reward</p>
                {loyaltyData ? (
                  <p className="text-4xl font-bold my-2">
                    eBa$ {loyaltyData.thisMonthRewards}
                  </p>
                ) : (
                  <Skeleton className="h-10 w-24 my-2 bg-white/20" />
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          {chartLoading ? (
            // Loading skeleton for charts
            [...Array(4)].map((_, index) => (
              <div key={index} className="bg-white p-4 rounded-lg shadow">
                <div className="flex justify-between items-center mb-4">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-3 w-20" />
                </div>
                <div className="h-48 flex items-end justify-around">
                  {[...Array(6)].map((_, i) => (
                    <Skeleton key={i} className="w-6 h-24" />
                  ))}
                </div>
              </div>
            ))
          ) : chartError ? (
            <div className="col-span-2 text-center py-8">
              <p className="text-red-500">Failed to load chart data</p>
            </div>
          ) : chartDataInfo && chartDataInfo.loyalty ? (
            <>
              <BarChartCard
                title="Loyalty Rewards"
                data={chartDataInfo.loyalty || []}
                color="#0F2C59"
              />
              <BarChartCard
                title="Referral Commissions"
                data={chartDataInfo.referral || []}
                color="#4A5568"
              />
              <BarChartCard
                title="Rewards Redeemed"
                data={chartDataInfo.rewardsRedeemed || []}
                color="#0F2C59"
              />
              <BarChartCard
                title="Commissions Redeemed"
                data={chartDataInfo.commissionsRedeemed || []}
                color="#4A5568"
              />
            </>
          ) : (
            <div className="col-span-2 text-center py-8">
              <p className="text-gray-500">No chart data available</p>
            </div>
          )}
        </div>

        {/* Referral Program */}
        <div className="bg-white p-6 rounded-lg shadow mb-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Referral Program</h2>
            <UsersIcon className="h-6 w-6 text-gray-400" />
          </div>
          {referralLoading ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
              <div>
                <div className="mb-4">
                  <Skeleton className="h-4 w-32 mb-2" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="mb-6">
                  <Skeleton className="h-4 w-32 mb-2" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="flex gap-4">
                  <Skeleton className="h-10 w-24" />
                  <Skeleton className="h-10 w-32" />
                </div>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg space-y-4">
                <Skeleton className="h-16 w-full" />
                <Skeleton className="h-16 w-full" />
                <Skeleton className="h-20 w-full" />
              </div>
            </div>
          ) : referralError ? (
            <div className="text-center py-8">
              <p className="text-red-500">Failed to load referral data</p>
            </div>
          ) : referralInfo ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
              <div>
                <div className="mb-4">
                  <label className="text-sm font-medium text-gray-500">
                    Your Referral Code
                  </label>
                  <div className="relative mt-1">
                    <MkdInputV2 value={referralInfo.code} disabled>
                      <MkdInputV2.Field className="pr-10" />
                    </MkdInputV2>
                    <button
                      onClick={() => handleCopyToClipboard(referralInfo.code)}
                      className="absolute inset-y-0 right-0 flex items-center pr-3 hover:text-gray-600"
                    >
                      <DocumentDuplicateIcon className="h-5 w-5 text-gray-400" />
                    </button>
                  </div>
                </div>
                <div className="mb-6">
                  <label className="text-sm font-medium text-gray-500">
                    Your Referral Link
                  </label>
                  <div className="relative mt-1">
                    <MkdInputV2 value={referralInfo.link} disabled>
                      <MkdInputV2.Field className="pr-10" />
                    </MkdInputV2>
                    <button
                      onClick={() => handleCopyToClipboard(referralInfo.link)}
                      className="absolute inset-y-0 right-0 flex items-center pr-3 hover:text-gray-600"
                    >
                      <DocumentDuplicateIcon className="h-5 w-5 text-gray-400" />
                    </button>
                  </div>
                </div>
                <div className="flex gap-4">
                  <button
                    onClick={handleShareLink}
                    disabled={isSharing}
                    className="bg-[#0F2C59] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#1a3a6b] disabled:opacity-50"
                  >
                    {isSharing ? "Sharing..." : "Share Link"}
                  </button>
                  <button
                    onClick={handleInviteByEmail}
                    disabled={isInviting || emailInviteList.length === 0}
                    className="border border-[#0F2C59] text-[#0F2C59] px-6 py-2 rounded-lg font-semibold hover:bg-[#0F2C59] hover:text-white disabled:opacity-50"
                  >
                    {isInviting ? "Sending..." : "Invite by Email"}
                  </button>
                </div>
                {/* Email input for invitations */}
                <div className="mt-4">
                  <div className="flex gap-2">
                    <input
                      type="email"
                      placeholder="Enter email address"
                      value={emailInput}
                      onChange={(e) => setEmailInput(e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59]"
                    />
                    <button
                      onClick={handleAddEmail}
                      disabled={!emailInput}
                      className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50"
                    >
                      Add
                    </button>
                  </div>
                  {emailInviteList.length > 0 && (
                    <div className="mt-2">
                      <p className="text-sm text-gray-600 mb-1">Email list:</p>
                      <div className="flex flex-wrap gap-1">
                        {emailInviteList.map((email, index) => (
                          <span
                            key={index}
                            className="bg-gray-100 px-2 py-1 rounded text-sm"
                          >
                            {email}
                            <button
                              onClick={() =>
                                setEmailInviteList(
                                  emailInviteList.filter((_, i) => i !== index)
                                )
                              }
                              className="ml-1 text-red-500 hover:text-red-700"
                            >
                              ×
                            </button>
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg space-y-4">
                <div className="flex justify-between items-center">
                  <p className="text-sm text-gray-500">Total Referred Users</p>
                  <div>
                    <p className="font-bold text-right">
                      {referralInfo.totalReferred}
                    </p>
                    <p className="text-xs text-gray-400 text-right">
                      {referralInfo.activeThisMonth} active this month
                    </p>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-sm text-gray-500">Commissions Earned</p>
                  <div>
                    <p className="font-bold text-right">
                      eBa$ {referralInfo.commissionsEarned}
                    </p>
                    <p className="text-xs text-gray-400 text-right">
                      eBa$ {referralInfo.commissionsThisMonth} this month
                    </p>
                  </div>
                </div>
                <hr />
                <div>
                  <p className="text-sm font-semibold mb-2">
                    Commission Breakdown
                  </p>
                  <div className="flex justify-between text-sm">
                    <p className="text-gray-500">Sign-up bonus</p>
                    <p className="font-medium">
                      eBa$ {referralInfo.signupBonus}
                    </p>
                  </div>
                  <div className="flex justify-between text-sm">
                    <p className="text-gray-500">Transaction commission (2%)</p>
                    <p className="font-medium">
                      eBa$ {referralInfo.transactionCommission}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : null}
        </div>

        {/* Monthly Breakdown */}
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Monthly Breakdown</h2>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1 rounded border border-gray-300 px-3 py-1.5 text-sm">
                <select
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                  className="bg-transparent border-none outline-none"
                >
                  {[...Array(5)].map((_, i) => {
                    const year = new Date().getFullYear() - i;
                    return (
                      <option key={year} value={year}>
                        {year}
                      </option>
                    );
                  })}
                </select>
                <ChevronDownIcon className="h-4 w-4 text-gray-500" />
              </div>
              <button className="p-2 rounded border border-gray-300">
                <ArrowDownTrayIcon className="h-5 w-5 text-gray-600" />
              </button>
            </div>
          </div>
          <div className="overflow-x-auto">
            {monthlyBreakdownLoading ? (
              <div className="space-y-3">
                <div className="flex space-x-4">
                  {[...Array(6)].map((_, i) => (
                    <Skeleton key={i} className="h-4 w-20" />
                  ))}
                </div>
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="flex space-x-4">
                    {[...Array(6)].map((_, j) => (
                      <Skeleton key={j} className="h-8 w-20" />
                    ))}
                  </div>
                ))}
              </div>
            ) : monthlyBreakdownError ? (
              <div className="text-center py-8">
                <p className="text-red-500">Failed to load monthly breakdown</p>
              </div>
            ) : (
              <table className="w-full text-sm">
                <thead className="bg-gray-50/50">
                  <tr className="text-left text-gray-500 font-medium">
                    <th className="py-2 px-3">Month</th>
                    <th className="py-2 px-3">Reward Earned</th>
                    <th className="py-2 px-3">Referral Commission</th>
                    <th className="py-2 px-3">Total Points</th>
                    <th className="py-2 px-3">Loyalty Status</th>
                    <th className="py-2 px-3">Commission Status</th>
                  </tr>
                </thead>
                <tbody>
                  {monthlyBreakdown.length === 0 ? (
                    <tr>
                      <td
                        colSpan={6}
                        className="text-center py-8 text-gray-500"
                      >
                        No data available for {selectedYear}
                      </td>
                    </tr>
                  ) : (
                    monthlyBreakdown.map((row: any, i: number) => (
                      <tr key={i} className="border-b border-gray-100">
                        <td className="py-3 px-3 font-semibold text-gray-700">
                          {row.month}
                        </td>
                        <td className="py-3 px-3">{row.reward}</td>
                        <td className="py-3 px-3">{row.commission}</td>
                        <td className="py-3 px-3">{row.points}</td>
                        <td className="py-3 px-3">
                          <span
                            className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              row.loyaltyStatus === "Paid"
                                ? "bg-green-100 text-green-800"
                                : "bg-yellow-100 text-yellow-800"
                            }`}
                          >
                            {row.loyaltyStatus}
                          </span>
                        </td>
                        <td className="py-3 px-3">
                          <span
                            className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              row.commissionStatus === "Paid"
                                ? "bg-green-100 text-green-800"
                                : "bg-gray-100 text-gray-700"
                            }`}
                          >
                            {row.commissionStatus}
                          </span>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            )}
          </div>
        </div>
      </div>
    </MemberWrapper>
  );
};

export default MemberRewardsListPage;
