import React, { useEffect } from "react";
import { AdminWrapper } from "../../../components/AdminWrapper";
import {
  CaptureSpatialIcon,
  UserIcon,
  FolderIcon,
  StarIcon,
  ShippingIcon,
  VerifiedIcon,
} from "../../../assets/svgs";
import { Link, useNavigate, useParams } from "react-router-dom";
import { useTransactionDetails } from "../../../hooks/useTransactionDetails";
import { MkdLoader } from "../../../components/MkdLoader";
import { useContexts } from "../../../hooks/useContexts";

const AdminViewTransactionPage = () => {
  const navigate = useNavigate();
  const { transactionDetails, loading, error } = useTransactionDetails();
  const { globalDispatch } = useContexts();

  useEffect(() => {
    if (error) {
      globalDispatch({
        type: "SNACKBAR",
        payload: {
          message: error.message,
          toastStatus: "error",
        },
      });
    }
  }, [error, globalDispatch]);

  if (loading) {
    return (
      <AdminWrapper>
        <div className="flex justify-center items-center h-screen">
          <MkdLoader />
        </div>
      </AdminWrapper>
    );
  }

  if (error || !transactionDetails) {
    return (
      <AdminWrapper>
        <div className="text-center p-6">
          <p>Could not load transaction details.</p>
          {error && <p className="text-red-500 mt-2">Error: {error.message}</p>}
        </div>
      </AdminWrapper>
    );
  }

  console.log("Transaction details received:", transactionDetails);
  const { summary, buyer, seller, fees, shipping } = transactionDetails;

  return (
    <AdminWrapper>
      <div className="p-8 bg-gray-50 min-h-screen text-[#0F2C59]">
        <div className="mb-10">
          <h1 className="text-4xl font-bold">View transaction details</h1>
          <p className="text-gray-500 mt-2 text-base">
            Moderate and manage marketplace transactions
          </p>
        </div>

        <div className="space-y-8">
          {/* Transaction Summary */}
          <div className="bg-white p-8 rounded-2xl shadow-sm">
            <h2 className="text-xl font-bold mb-6">Transaction Summary</h2>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
              <div className="lg:col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-x-8 gap-y-6">
                <div>
                  <p className="text-sm text-gray-500">Transaction ID</p>
                  <p className="font-bold text-lg mt-1">#{summary.id}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Date</p>
                  <p className="font-bold text-lg mt-1">{summary.date}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Type</p>
                  <span className="mt-1 inline-block px-3 py-1 rounded-md text-sm font-semibold bg-green-100 text-green-700 capitalize">
                    {summary.type}
                  </span>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Status</p>
                  <span
                    className={`mt-1 inline-block px-3 py-1 rounded-md text-sm font-semibold capitalize ${
                      summary.status === "completed"
                        ? "bg-green-100 text-green-700"
                        : "bg-yellow-100 text-yellow-700"
                    }`}
                  >
                    {summary.status}
                  </span>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Price</p>
                  <p className="font-bold text-lg mt-1">eBa$ {summary.price}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Listing Link</p>
                  <Link
                    to={`/admin/view-listing/${summary.listing_id}`}
                    className="text-blue-600 hover:underline font-semibold mt-1 inline-block"
                  >
                    View listing
                  </Link>
                </div>
              </div>
              <div className="bg-gray-50 p-4 rounded-xl flex items-center w-full lg:max-w-xs">
                <div className="mr-4">
                  <CaptureSpatialIcon className="w-8 h-8 text-gray-500" />
                </div>
                <div>
                  <p className="font-bold text-base">{summary.listing_name}</p>
                  <p className="text-sm text-gray-500">
                    {summary.listing_description}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Buyer & Seller Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white p-8 rounded-2xl shadow-sm">
              <h3 className="text-lg font-bold mb-6 flex items-center">
                <UserIcon className="w-6 h-6 mr-3 text-blue-500" />
                Buyer Details
              </h3>
              <div className="space-y-5">
                <div>
                  <p className="text-sm text-gray-500">Name</p>
                  <p className="font-bold text-base mt-1">{buyer.name}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Credit Score</p>
                  <p className="font-bold text-base mt-1">
                    {buyer.credit_score}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Location</p>
                  <p className="font-bold text-base mt-1">{buyer.location}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Member since</p>
                  <p className="font-bold text-base mt-1">
                    {buyer.member_since}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Status</p>
                  <p
                    className={`font-semibold flex items-center mt-1 ${
                      buyer.is_verified ? "text-green-600" : "text-yellow-600"
                    }`}
                  >
                    <VerifiedIcon className="w-5 h-5 mr-2" />
                    {buyer.is_verified ? "Verified" : "Not Verified"}
                  </p>
                </div>
              </div>
            </div>
            {seller && (
              <div className="bg-white p-8 rounded-2xl shadow-sm">
                <h3 className="text-lg font-bold mb-6 flex items-center">
                  <FolderIcon className="w-6 h-6 mr-3 text-orange-500" />
                  Seller Details
                </h3>
                <div className="space-y-5">
                  <div>
                    <p className="text-sm text-gray-500">Name</p>
                    <p className="font-bold text-base mt-1">{seller.name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Rating</p>
                    <p className="font-bold flex items-center mt-1">
                      <StarIcon className="w-5 h-5 mr-1 text-yellow-400" />
                      {seller.rating.rating} ({seller.rating.review_count}{" "}
                      reviews)
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Location</p>
                    <p className="font-bold text-base mt-1">
                      {seller.location}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Seller Type</p>
                    <p className="font-bold text-base mt-1">{seller.role}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Status</p>
                    <p
                      className={`font-semibold flex items-center mt-1 ${
                        seller.is_verified
                          ? "text-green-600"
                          : "text-yellow-600"
                      }`}
                    >
                      <VerifiedIcon className="w-5 h-5 mr-2" />
                      {seller.is_verified ? "Verified" : "Not Verified"}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Fee Breakdown */}
          <div className="bg-white p-8 rounded-2xl shadow-sm">
            <h2 className="text-xl font-bold mb-6">Fee Breakdown</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h4 className="font-bold text-lg mb-4 text-blue-600">
                  Buyer Side
                </h4>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <p className="text-gray-500">Gross Amount</p>
                    <p className="font-bold">eBa$ {fees.buyer.gross_amount}</p>
                  </div>
                  <div className="flex justify-between">
                    <p className="text-gray-500">USD Fee</p>
                    <p className="font-bold">${fees.buyer.usd_fee}</p>
                  </div>
                  <div className="flex justify-between">
                    <p className="text-gray-500">EBA Fee</p>
                    <p className="font-bold">
                      {fees.buyer.eba_fee > 0
                        ? `eBa$ ${fees.buyer.eba_fee}`
                        : "—"}
                    </p>
                  </div>
                  <hr className="my-4" />
                  <div className="flex justify-between font-bold text-lg">
                    <p>Total Paid</p>
                    <p>
                      eBa$ {fees.buyer.gross_amount} + ${fees.buyer.usd_fee}
                    </p>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-bold text-lg mb-4 text-[#E63946]">
                  Seller Side
                </h4>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <p className="text-gray-500">Gross Amount</p>
                    <p className="font-bold">eBa$ {fees.seller.gross_amount}</p>
                  </div>
                  <div className="flex justify-between">
                    <p className="text-gray-500">USD Fee</p>
                    <p className="font-bold">
                      {fees.seller.usd_fee > 0
                        ? `$${fees.seller.usd_fee}`
                        : "—"}
                    </p>
                  </div>
                  <div className="flex justify-between">
                    <p className="text-gray-500">EBA Fee</p>
                    <p className="font-bold">eBa$ {fees.seller.eba_fee}</p>
                  </div>
                  <hr className="my-4" />
                  <div className="flex justify-between font-bold text-lg">
                    <p>Net Received</p>
                    <p>eBa$ {fees.seller.gross_amount - fees.seller.eba_fee}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Shipping & Delivery */}
          <div className="bg-white p-8 rounded-2xl shadow-sm">
            <h2 className="text-xl font-bold mb-6 flex items-center">
              <ShippingIcon className="w-6 h-6 mr-3 text-green-500" />
              Shipping & Delivery
            </h2>
            <div className="flex flex-wrap -mx-4">
              <div className="w-full lg:w-1/2 px-4 space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500 mb-1">
                      Shipping Method
                    </p>
                    <p className="font-bold text-base">{shipping.method}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-1">
                      Delivery Status
                    </p>
                    <span
                      className={`inline-block px-3 py-1 rounded-md text-sm font-semibold capitalize ${
                        shipping.status === "delivered"
                          ? "bg-green-100 text-green-700"
                          : "bg-yellow-100 text-yellow-700"
                      }`}
                    >
                      {shipping.status}
                    </span>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-1">
                      Tracking Number
                    </p>
                    <p className="font-bold text-base">
                      {shipping.tracking_number}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-1">
                      Confirmation Code
                    </p>
                    <p className="font-bold text-base">
                      {shipping.confirmation_code}
                    </p>
                  </div>
                </div>
              </div>
              {shipping.address && (
                <div className="w-full lg:w-1/2 px-4 mt-6 lg:mt-0">
                  <div className="bg-gray-50 p-4 rounded-xl h-full">
                    <p className="text-sm text-gray-500 mb-1">
                      Delivery Address
                    </p>
                    <p className="font-bold text-base leading-relaxed">
                      {shipping.address.street}
                      <br />
                      {shipping.address.city}, {shipping.address.state}{" "}
                      {shipping.address.zip}
                      <br />
                      {shipping.address.country}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </AdminWrapper>
  );
};

export default AdminViewTransactionPage;
