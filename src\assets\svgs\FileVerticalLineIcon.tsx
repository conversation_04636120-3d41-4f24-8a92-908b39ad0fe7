import React from 'react';

const FileVerticalLineIcon = ({ className = "", fill = "#A8A8A8", onClick = () => { } }) => {
    return (
        <svg onClick={onClick} className={`${className}`} xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path fillRule="evenodd" clipRule="evenodd" d="M4.79165 1.66675H9.99998V6.87508C9.99998 7.6805 10.6529 8.33342 11.4583 8.33342H16.6666V16.8751C16.6666 17.6805 16.0137 18.3334 15.2083 18.3334H4.79165C3.98623 18.3334 3.33331 17.6805 3.33331 16.8751V3.12508C3.33331 2.31967 3.98623 1.66675 4.79165 1.66675ZM7.70831 13.9584C7.70831 13.6132 7.42849 13.3334 7.08331 13.3334C6.73813 13.3334 6.45831 13.6132 6.45831 13.9584V15.2084C6.45831 15.5536 6.73813 15.8334 7.08331 15.8334C7.42849 15.8334 7.70831 15.5536 7.70831 15.2084V13.9584ZM9.99998 10.8334C10.3452 10.8334 10.625 11.1132 10.625 11.4584V15.2084C10.625 15.5536 10.3452 15.8334 9.99998 15.8334C9.6548 15.8334 9.37498 15.5536 9.37498 15.2084V11.4584C9.37498 11.1132 9.6548 10.8334 9.99998 10.8334ZM13.5416 13.1251C13.5416 12.7799 13.2618 12.5001 12.9166 12.5001C12.5715 12.5001 12.2916 12.7799 12.2916 13.1251V15.2084C12.2916 15.5536 12.5715 15.8334 12.9166 15.8334C13.2618 15.8334 13.5416 15.5536 13.5416 15.2084V13.1251Z" fill={fill} />
            <path d="M16.2395 7.0224C16.2593 7.04223 16.2786 7.06258 16.2971 7.08342H11.4583C11.3433 7.08342 11.25 6.99014 11.25 6.87508V2.03628C11.2708 2.05484 11.2912 2.07405 11.311 2.09388L16.2395 7.0224Z" fill={fill} />
        </svg>
    );
};

export default FileVerticalLineIcon;