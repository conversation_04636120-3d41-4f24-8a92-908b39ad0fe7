import { useState, useRef, useEffect, Fragment, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { MemberWrapper } from "../../../components/MemberWrapper";
import InteractiveButton from "../../../components/InteractiveButton/InteractiveButton";
import { MkdInputV2 } from "../../../components/MkdInputV2";
import { useSDK } from "../../../hooks/useSDK";
import { useToast } from "../../../hooks/useToast";
import { Modal } from "../../../components/Modal";
import { MkdLoader } from "../../../components/MkdLoader";

interface IListingForm {
  // Basic Info
  listingType: string;
  title: string;
  description: string;
  category: string;
  images: File[];

  // Price & Stock
  price: string;
  discountPrice: string;
  quantity: string;

  // Location
  addressLine: string;
  city: string;
  country: string;

  // Shipping
  length: string;
  width: string;
  height: string;
  weight: string;
  fragileHandling: boolean;
  flammableGas: boolean;
  liquidItems: boolean;
  glassBreakable: boolean;
  ebaDelivery: boolean;
  localDelivery: boolean;
  internationalDelivery: boolean;
  pickup: boolean;
  meetup: boolean;
  sizes: Record<string, { selected: boolean; quantity: string }>;
  // Service Fields
  schedulingType: "slots" | "flexible";
  availability: Record<
    string,
    { selected: boolean; slots: { start: string; end: string }[] }
  >;
  serviceLocationType: "provider" | "buyer" | "choice";
  paymentTerms: "before" | "after";
}

const MemberEditListingPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { sdk } = useSDK();
  const { success, error: showError } = useToast();

  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(1);

  const availableSizes = ["XS", "S", "M", "L", "XL", "XXL", "XXXL"];
  const daysOfWeek = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday",
  ];

  const initialSizes = availableSizes.reduce(
    (acc, size) => {
      acc[size] = { selected: false, quantity: "" };
      return acc;
    },
    {} as Record<string, { selected: boolean; quantity: string }>
  );

  const initialAvailability = daysOfWeek.reduce(
    (acc, day) => {
      acc[day] = {
        selected: false,
        slots: [{ start: "09:00", end: "18:00" }],
      };
      return acc;
    },
    {} as Record<
      string,
      { selected: boolean; slots: { start: string; end: string }[] }
    >
  );

  const [formData, setFormData] = useState<IListingForm>({
    // Basic Info
    listingType: "Item",
    title: "",
    description: "",
    category: "",
    images: [],

    // Price & Stock
    price: "",
    discountPrice: "",
    quantity: "1",

    // Location
    addressLine: "",
    city: "",
    country: "",

    // Shipping
    length: "",
    width: "",
    height: "",
    weight: "",
    fragileHandling: false,
    flammableGas: false,
    liquidItems: false,
    glassBreakable: false,
    ebaDelivery: true,
    localDelivery: false,
    internationalDelivery: false,
    pickup: false,
    meetup: false,
    sizes: initialSizes,

    // Service Fields
    schedulingType: "slots",
    availability: initialAvailability,
    serviceLocationType: "provider",
    paymentTerms: "before",
  });

  const [categories, setCategories] = useState<any[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load listing data and categories
  useEffect(() => {
    if (id) {
      loadListingData();
      loadCategories();
    }
  }, [id]);

  const loadListingData = async () => {
    setInitialLoading(true);
    try {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/listings/${id}`,
        method: "GET",
      });

      if (!response.error && response.data) {
        const listing = response.data;
        setFormData({
          // Basic Info
          listingType: listing.listing_type || "Item",
          title: listing.name || "",
          description: listing.description || "",
          category: listing.category || "",
          images: [], // Will be handled separately for existing images

          // Price & Stock
          price: listing.price?.toString() || "",
          discountPrice: listing.discountPrice?.toString() || "",
          quantity: listing.quantity?.toString() || "1",

          // Location - Set defaults since these might not be in the existing data
          addressLine: "",
          city: "",
          country: "",

          // Shipping - Set defaults
          length: "",
          width: "",
          height: "",
          weight: "",
          fragileHandling: false,
          flammableGas: false,
          liquidItems: false,
          glassBreakable: false,
          ebaDelivery: true,
          localDelivery: false,
          internationalDelivery: false,
          pickup: false,
          meetup: false,
          sizes: initialSizes,

          // Service Fields - Set defaults
          schedulingType: "slots",
          availability: initialAvailability,
          serviceLocationType: "provider",
          paymentTerms: "before",
        });
      } else {
        setError("Failed to load listing data");
      }
    } catch (error) {
      console.error("Error loading listing:", error);
      setError("Failed to load listing data");
    } finally {
      setInitialLoading(false);
    }
  };

  const loadCategories = async () => {
    try {
      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/categories",
        method: "GET",
      });

      if (!response.error && response.data) {
        setCategories(response.data);
      }
    } catch (error) {
      console.error("Error loading categories:", error);
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!formData.title.trim()) {
        throw new Error("Title is required");
      }
      if (!formData.description.trim()) {
        throw new Error("Description is required");
      }
      if (!formData.price || parseFloat(formData.price) <= 0) {
        throw new Error("Valid price is required");
      }
      if (!formData.category) {
        throw new Error("Category is required");
      }

      // Prepare the update data
      const updateData = {
        name: formData.title,
        description: formData.description,
        price: parseFloat(formData.price),
        discount_price: formData.discountPrice
          ? parseFloat(formData.discountPrice)
          : undefined,
        quantity: parseInt(formData.quantity) || 1,
        listing_type: formData.listingType,
        category: formData.category,
        status: "active", // Keep as active when updating
      };

      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/listings/${id}`,
        method: "PUT",
        body: updateData,
      });

      if (!response.error) {
        success("Listing updated successfully!");
        navigate("/member/listings");
      } else {
        throw new Error(response.message || "Failed to update listing");
      }
    } catch (error: any) {
      console.error("Error updating listing:", error);
      setError(error.message || "Failed to update listing");
      showError(error.message || "Failed to update listing");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof IListingForm, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length > 0) {
      setFormData((prev) => ({
        ...prev,
        images: [...prev.images, ...files].slice(0, 5), // Limit to 5 images
      }));
    }
  };

  const removeImage = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index),
    }));
  };

  // Loading state
  if (initialLoading) {
    return (
      <MemberWrapper>
        <div className="p-6 bg-[#001f3f] min-h-screen">
          <div className="flex justify-center items-center py-12">
            <MkdLoader />
          </div>
        </div>
      </MemberWrapper>
    );
  }

  // Error state
  if (error) {
    return (
      <MemberWrapper>
        <div className="p-6 bg-[#001f3f] min-h-screen">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg p-6">
              <div className="text-center">
                <div className="text-red-600 text-lg mb-4">
                  Error loading listing
                </div>
                <p className="text-gray-600 mb-4">{error}</p>
                <InteractiveButton
                  onClick={() => navigate("/member/listings")}
                  className="bg-[#e53e3e] text-white px-6 py-2 rounded-md hover:bg-[#c53030]"
                >
                  Back to Listings
                </InteractiveButton>
              </div>
            </div>
          </div>
        </div>
      </MemberWrapper>
    );
  }

  const steps = [
    { number: 1, title: "Basic Info", active: currentStep === 1 },
    { number: 2, title: "Price & Stock", active: currentStep === 2 },
    { number: 3, title: "Images", active: currentStep === 3 },
  ];

  return (
    <MemberWrapper>
      <div className="p-6 bg-[#001f3f] min-h-screen">
        <div className="mb-6">
          <InteractiveButton
            onClick={() => navigate("/member/listings")}
            className="text-white hover:text-gray-300 mb-4"
          >
            ← Back to My Listings
          </InteractiveButton>
          <h1 className="text-3xl font-bold text-white mb-2">Edit Listing</h1>
          <p className="text-gray-300">Update your listing details</p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-8">
            {steps.map((step, index) => (
              <div key={step.number} className="flex items-center">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                    step.active
                      ? "bg-[#e53e3e] text-white"
                      : currentStep > step.number
                        ? "bg-green-500 text-white"
                        : "bg-gray-300 text-gray-600"
                  }`}
                >
                  {currentStep > step.number ? "✓" : step.number}
                </div>
                <span
                  className={`ml-2 text-sm font-medium ${
                    step.active ? "text-white" : "text-gray-400"
                  }`}
                >
                  {step.title}
                </span>
                {index < steps.length - 1 && (
                  <div className="w-16 h-0.5 bg-gray-300 ml-4"></div>
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg p-6">
          <div className="space-y-6">
            {/* Step 1: Basic Info */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Basic Information
                </h2>

                {/* Listing Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Listing Type *
                  </label>
                  <div className="grid grid-cols-2 gap-4">
                    {["Item", "Service"].map((type) => (
                      <button
                        key={type}
                        type="button"
                        onClick={() => handleInputChange("listingType", type)}
                        className={`p-4 border-2 rounded-lg text-center transition-colors ${
                          formData.listingType === type
                            ? "border-[#e53e3e] bg-[#e53e3e]/10 text-[#e53e3e]"
                            : "border-gray-300 hover:border-gray-400"
                        }`}
                      >
                        <div className="font-medium">{type}</div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Title */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Title *
                  </label>
                  <MkdInputV2
                    placeholder="Enter listing title"
                    value={formData.title}
                    onChange={(e) => handleInputChange("title", e.target.value)}
                    required
                  >
                    <MkdInputV2.Field />
                  </MkdInputV2>
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description *
                  </label>
                  <textarea
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#e53e3e] focus:border-transparent"
                    rows={4}
                    placeholder="Describe your listing in detail"
                    value={formData.description}
                    onChange={(e) =>
                      handleInputChange("description", e.target.value)
                    }
                    required
                  />
                </div>

                {/* Category */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category *
                  </label>
                  <select
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#e53e3e] focus:border-transparent"
                    value={formData.category}
                    onChange={(e) =>
                      handleInputChange("category", e.target.value)
                    }
                    required
                  >
                    <option value="">Select a category</option>
                    {categories.map((cat) => (
                      <option key={cat.id} value={cat.name}>
                        {cat.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            )}

            {/* Step 2: Price & Stock */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Price & Stock
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Price */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Price (eBa$) *
                    </label>
                    <MkdInputV2
                      type="number"
                      placeholder="0.00"
                      value={formData.price}
                      onChange={(e) =>
                        handleInputChange("price", e.target.value)
                      }
                      required
                    >
                      <MkdInputV2.Field />
                    </MkdInputV2>
                  </div>

                  {/* Discount Price */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Discount Price (eBa$)
                    </label>
                    <MkdInputV2
                      type="number"
                      placeholder="0.00"
                      value={formData.discountPrice}
                      onChange={(e) =>
                        handleInputChange("discountPrice", e.target.value)
                      }
                    >
                      <MkdInputV2.Field />
                    </MkdInputV2>
                  </div>
                </div>

                {/* Quantity */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Quantity *
                  </label>
                  <MkdInputV2
                    type="number"
                    placeholder="1"
                    value={formData.quantity}
                    onChange={(e) =>
                      handleInputChange("quantity", e.target.value)
                    }
                    required
                  >
                    <MkdInputV2.Field />
                  </MkdInputV2>
                </div>
              </div>
            )}

            {/* Step 3: Images */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Images
                </h2>

                {/* Image Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Upload Images (Max 5)
                  </label>
                  <div
                    className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-[#e53e3e] transition-colors"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <div className="text-gray-500">
                      <div className="text-lg mb-2">📷</div>
                      <div>Click to upload images</div>
                      <div className="text-sm">PNG, JPG up to 10MB each</div>
                    </div>
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                </div>

                {/* Image Preview */}
                {formData.images.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {formData.images.map((image, index) => (
                      <div key={index} className="relative">
                        <img
                          src={URL.createObjectURL(image)}
                          alt={`Preview ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg"
                        />
                        <button
                          type="button"
                          onClick={() => removeImage(index)}
                          className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between pt-6 border-t border-gray-200">
              <div>
                {currentStep > 1 && (
                  <InteractiveButton
                    onClick={() => setCurrentStep(currentStep - 1)}
                    className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                  >
                    Previous
                  </InteractiveButton>
                )}
              </div>

              <div className="flex gap-3">
                {currentStep < steps.length ? (
                  <InteractiveButton
                    onClick={() => setCurrentStep(currentStep + 1)}
                    className="px-6 py-2 bg-[#e53e3e] text-white rounded-md hover:bg-[#c53030]"
                  >
                    Next
                  </InteractiveButton>
                ) : (
                  <InteractiveButton
                    onClick={handleSubmit}
                    disabled={loading}
                    className="px-6 py-2 bg-[#e53e3e] text-white rounded-md hover:bg-[#c53030] disabled:opacity-50"
                  >
                    {loading ? "Updating..." : "Update Listing"}
                  </InteractiveButton>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </MemberWrapper>
  );
};

export default MemberEditListingPage;
