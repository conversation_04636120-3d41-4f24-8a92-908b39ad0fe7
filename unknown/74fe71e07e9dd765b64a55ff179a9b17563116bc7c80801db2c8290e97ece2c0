import React, { useState } from "react";
import {
  useDeliveryCalculationMutation,
  usePlatformFeeCalculationMutation,
  formatCurrency,
  validateDeliveryAddress,
  calculatePackageVolume,
  IDeliveryCalculationRequest,
  IPlatformFeeRequest,
} from "../query/useDeliveryCalculation";

interface IDeliveryCalculatorProps {
  listingId: number;
  listingPrice: number;
  listingCurrency?: string;
  onCalculationComplete?: (result: any) => void;
  className?: string;
}

export const DeliveryCalculator: React.FC<IDeliveryCalculatorProps> = ({
  listingId,
  listingPrice,
  listingCurrency = "eBa$",
  onCalculationComplete,
  className = "",
}) => {
  const [deliveryAddress, setDeliveryAddress] = useState("");
  const [packageDetails, setPackageDetails] = useState({
    weightKg: 1.0,
    lengthCm: 10,
    widthCm: 10,
    heightCm: 10,
  });
  const [exchangeRate, setExchangeRate] = useState(0.85);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [calculationResult, setCalculationResult] = useState<any>(null);

  const deliveryMutation = useDeliveryCalculationMutation();
  const feeMutation = usePlatformFeeCalculationMutation();

  const handleCalculate = async () => {
    if (!validateDeliveryAddress(deliveryAddress)) {
      alert("Please enter a valid delivery address (minimum 10 characters)");
      return;
    }

    try {
      // Calculate delivery fees and deadline
      const deliveryRequest: IDeliveryCalculationRequest = {
        listingId,
        deliveryAddress,
        ...packageDetails,
      };

      const deliveryResult =
        await deliveryMutation.mutateAsync(deliveryRequest);

      // Calculate platform fees
      const feeRequest: IPlatformFeeRequest = {
        listingId,
        saleAmount: listingPrice,
        exchangeRate,
      };

      const feeResult = await feeMutation.mutateAsync(feeRequest);

      // Combine results
      const combinedResult = {
        delivery: deliveryResult,
        fees: feeResult,
        totalCost: {
          itemPrice: listingPrice,
          deliveryFee:
            (deliveryResult as any)?.data?.delivery?.summary?.totalFee || 0,
          subtotal:
            listingPrice +
            ((deliveryResult as any)?.data?.delivery?.summary?.totalFee || 0),
          sellerFee: (feeResult as any)?.data?.seller?.feeAmount || 0,
          buyerFeeUsd: (feeResult as any)?.data?.buyer?.feeAmountUsd || 0,
          sellerReceives: (feeResult as any)?.data?.seller?.netAmount || 0,
          buyerPaysUsd: (feeResult as any)?.data?.buyer?.totalAmountUsd || 0,
        },
      };

      setCalculationResult(combinedResult);
      onCalculationComplete?.(combinedResult);
    } catch (error) {
      console.error("Calculation error:", error);
      alert("Failed to calculate delivery and fees. Please try again.");
    }
  };

  const isLoading = deliveryMutation.isPending || feeMutation.isPending;

  return (
    <div className={`bg-white rounded-lg border p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Delivery & Fee Calculator
      </h3>

      {/* Delivery Address */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Delivery Address *
        </label>
        <textarea
          value={deliveryAddress}
          onChange={(e) => setDeliveryAddress(e.target.value)}
          placeholder="Enter your complete delivery address..."
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={3}
        />
      </div>

      {/* Advanced Options Toggle */}
      <button
        type="button"
        onClick={() => setShowAdvanced(!showAdvanced)}
        className="text-blue-600 hover:text-blue-800 text-sm font-medium mb-4"
      >
        {showAdvanced ? "Hide" : "Show"} Advanced Options
      </button>

      {/* Advanced Package Details */}
      {showAdvanced && (
        <div className="mb-4 p-4 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-3">
            Package Details
          </h4>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-xs font-medium text-gray-600 mb-1">
                Weight (kg)
              </label>
              <input
                type="number"
                value={packageDetails.weightKg}
                onChange={(e) =>
                  setPackageDetails({
                    ...packageDetails,
                    weightKg: parseFloat(e.target.value) || 1.0,
                  })
                }
                min="0.1"
                step="0.1"
                className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-600 mb-1">
                Length (cm)
              </label>
              <input
                type="number"
                value={packageDetails.lengthCm}
                onChange={(e) =>
                  setPackageDetails({
                    ...packageDetails,
                    lengthCm: parseInt(e.target.value) || 10,
                  })
                }
                min="1"
                className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-600 mb-1">
                Width (cm)
              </label>
              <input
                type="number"
                value={packageDetails.widthCm}
                onChange={(e) =>
                  setPackageDetails({
                    ...packageDetails,
                    widthCm: parseInt(e.target.value) || 10,
                  })
                }
                min="1"
                className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-gray-600 mb-1">
                Height (cm)
              </label>
              <input
                type="number"
                value={packageDetails.heightCm}
                onChange={(e) =>
                  setPackageDetails({
                    ...packageDetails,
                    heightCm: parseInt(e.target.value) || 10,
                  })
                }
                min="1"
                className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
              />
            </div>
          </div>

          <div className="mt-3 text-xs text-gray-600">
            Volume:{" "}
            {calculatePackageVolume(
              packageDetails.lengthCm,
              packageDetails.widthCm,
              packageDetails.heightCm
            ).toLocaleString()}{" "}
            cm³
          </div>

          {/* Exchange Rate */}
          <div className="mt-4">
            <label className="block text-xs font-medium text-gray-600 mb-1">
              eBa$ to USD Exchange Rate
            </label>
            <input
              type="number"
              value={exchangeRate}
              onChange={(e) =>
                setExchangeRate(parseFloat(e.target.value) || 0.85)
              }
              min="0.01"
              step="0.01"
              className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
            />
          </div>
        </div>
      )}

      {/* Calculate Button */}
      <button
        onClick={handleCalculate}
        disabled={isLoading || !deliveryAddress.trim()}
        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
      >
        {isLoading ? "Calculating..." : "Calculate Delivery & Fees"}
      </button>

      {/* Results */}
      {calculationResult && (
        <div className="mt-6 p-4 bg-green-50 rounded-lg">
          <h4 className="text-lg font-semibold text-green-800 mb-3">
            Calculation Results
          </h4>

          {/* Delivery Information */}
          <div className="mb-4">
            <h5 className="font-medium text-gray-700 mb-2">Delivery Details</h5>
            <div className="text-sm text-gray-600 space-y-1">
              <div>
                Distance:{" "}
                {calculationResult.delivery.delivery.summary.distanceKm} km
              </div>
              <div>
                Estimated Time:{" "}
                {calculationResult.delivery.delivery.summary.estimatedHours}{" "}
                hours
              </div>
              <div>
                Delivery Deadline:{" "}
                {new Date(
                  calculationResult.delivery.delivery.summary.deliveryDeadline
                ).toLocaleString()}
              </div>
              <div>
                Delivery Fee:{" "}
                {formatCurrency(
                  calculationResult.delivery.delivery.summary.totalFee
                )}
              </div>
            </div>
          </div>

          {/* Cost Breakdown */}
          <div className="mb-4">
            <h5 className="font-medium text-gray-700 mb-2">Cost Breakdown</h5>
            <div className="text-sm text-gray-600 space-y-1">
              <div className="flex justify-between">
                <span>Item Price:</span>
                <span>
                  {formatCurrency(
                    calculationResult.totalCost.itemPrice,
                    listingCurrency
                  )}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Delivery Fee:</span>
                <span>
                  {formatCurrency(calculationResult.totalCost.deliveryFee)}
                </span>
              </div>
              <div className="flex justify-between border-t pt-1">
                <span>Subtotal:</span>
                <span>
                  {formatCurrency(
                    calculationResult.totalCost.subtotal,
                    listingCurrency
                  )}
                </span>
              </div>
            </div>
          </div>

          {/* Platform Fees */}
          <div>
            <h5 className="font-medium text-gray-700 mb-2">Platform Fees</h5>
            <div className="text-sm text-gray-600 space-y-1">
              <div className="flex justify-between">
                <span>Seller Receives:</span>
                <span className="text-green-600 font-medium">
                  {formatCurrency(
                    calculationResult.totalCost.sellerReceives,
                    listingCurrency
                  )}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Buyer Pays (USD):</span>
                <span className="text-blue-600 font-medium">
                  {formatCurrency(
                    calculationResult.totalCost.buyerPaysUsd,
                    "USD"
                  )}
                </span>
              </div>
              <div className="flex justify-between text-xs">
                <span>Seller Fee:</span>
                <span>
                  {formatCurrency(
                    calculationResult.totalCost.sellerFee,
                    listingCurrency
                  )}
                </span>
              </div>
              <div className="flex justify-between text-xs">
                <span>Buyer Fee:</span>
                <span>
                  {formatCurrency(
                    calculationResult.totalCost.buyerFeeUsd,
                    "USD"
                  )}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {(deliveryMutation.error || feeMutation.error) && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-600 text-sm">
            {deliveryMutation.error?.message || feeMutation.error?.message}
          </p>
        </div>
      )}
    </div>
  );
};
