import React from "react";
import { useForm } from "react-hook-form";
import { AdminWrapper } from "../../../components/AdminWrapper";
import { InteractiveButton } from "../../../components/InteractiveButton";
import { MkdInputV2 } from "../../../components/MkdInputV2";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useAddCategoryMutation } from "../../../query/useCategory";
import { useNavigate } from "react-router-dom";

const schema = yup.object({
  name: yup.string().required("Category name is required"),
});

const AdminAddCategoryPage = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });
  const navigate = useNavigate();
  const { mutate, isPending } = useAddCategoryMutation();

  const onSubmit = (data: any) => {
    mutate(data, {
      onSuccess: () => {
        navigate("/admin/categories");
      },
    });
  };

  return (
    <AdminWrapper>
      <div className="p-6 bg-[#F8F9FB] min-h-screen">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-[#111827]">
            Create new category
          </h1>
          <img
            src="https://randomuser.me/api/portraits/women/68.jpg"
            alt="User"
            className="w-10 h-10 rounded-full"
          />
        </div>

        <div className="bg-white p-8 rounded-lg shadow-md max-w-lg">
          <form onSubmit={handleSubmit(onSubmit)}>
            <MkdInputV2
              register={register}
              errors={errors}
              name="name"
              placeholder="Enter name here"
              className="w-full"
            >
              <MkdInputV2.Label className="font-semibold text-gray-700 mb-2 block">
                Category Name
              </MkdInputV2.Label>
              <MkdInputV2.Field className="!py-2.5 !px-4 !border !border-[#D1D5DB] !rounded-md w-full !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59]" />
              <MkdInputV2.Error className="text-red-500 text-sm mt-1" />
            </MkdInputV2>

            <InteractiveButton
              type="submit"
              className="!bg-[#1E293B] hover:bg-gray-900 text-white font-semibold py-3 rounded-md w-full mt-6"
              loading={isPending}
            >
              Save changes
            </InteractiveButton>
          </form>
        </div>
      </div>
    </AdminWrapper>
  );
};

export default AdminAddCategoryPage;
