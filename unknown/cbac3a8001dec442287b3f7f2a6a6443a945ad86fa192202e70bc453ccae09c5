import React, { useState, useEffect } from "react";
import { Skeleton } from "../../../components/Skeleton";
import {
  useStartChatMutation,
  useSendChatMessageMutation,
  useChatMessagesQuery,
  useEndChatMutation,
} from "../../../query/useLanding";

const ChatWidget = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState("");
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [userName, setUserName] = useState("");
  const [userEmail, setUserEmail] = useState("");
  const [isStarted, setIsStarted] = useState(false);

  // Mutations
  const { mutate: startChat, isPending: isStarting } = useStartChatMutation();
  const { mutate: sendMessage, isPending: isSending } =
    useSendChatMessageMutation();
  const { mutate: endChat } = useEndChatMutation();

  // Query for messages (only when session exists)
  const { data: messagesData, isLoading: messagesLoading } =
    useChatMessagesQuery(sessionId || "");

  const messages = messagesData?.data || [];

  const handleChatClick = () => {
    setIsOpen(true);
  };

  const handleClose = () => {
    if (sessionId) {
      endChat(sessionId);
      setSessionId(null);
      setIsStarted(false);
    }
    setIsOpen(false);
  };

  const handleStartChat = () => {
    if (!userName.trim()) return;

    startChat(
      { userName, userEmail },
      {
        onSuccess: (data) => {
          setSessionId(data.data.sessionId);
          setIsStarted(true);
        },
      }
    );
  };

  const handleSendMessage = () => {
    if (message.trim() && sessionId) {
      sendMessage(
        {
          sessionId,
          message: message.trim(),
          senderName: userName,
        },
        {
          onSuccess: () => {
            setMessage("");
          },
        }
      );
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      {/* Chat Modal */}
      {isOpen && (
        <div className="fixed bottom-6 right-6 z-50 w-80 bg-white rounded-lg shadow-2xl border border-gray-200">
          {/* Header */}
          <div
            className="flex items-center justify-between p-4 rounded-t-lg"
            style={{ backgroundColor: "#F52D2A" }}
          >
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              <div>
                <h3 className="text-white font-medium text-sm">
                  eBaDollar Assistant
                </h3>
                <p className="text-white text-xs opacity-90">Online now</p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-white hover:opacity-80 text-lg font-bold"
            >
              ×
            </button>
          </div>

          {/* Chat Content */}
          <div className="p-4 h-64 overflow-y-auto bg-gray-50">
            {!isStarted ? (
              // Start chat form
              <div className="space-y-4">
                <p className="text-gray-700 text-sm mb-3">
                  Hi! I'm here to help. Please enter your details to start
                  chatting:
                </p>

                <div>
                  <input
                    type="text"
                    placeholder="Your name"
                    value={userName}
                    onChange={(e) => setUserName(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <input
                    type="email"
                    placeholder="Your email (optional)"
                    value={userEmail}
                    onChange={(e) => setUserEmail(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <button
                  onClick={handleStartChat}
                  disabled={!userName.trim() || isStarting}
                  className="w-full bg-[#F52D2A] text-white py-2 px-4 rounded-md text-sm hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isStarting ? "Starting..." : "Start Chat"}
                </button>
              </div>
            ) : (
              // Chat messages
              <div className="space-y-3">
                {messagesLoading ? (
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                ) : (
                  messages.map((msg: any, index: number) => (
                    <div
                      key={msg.id || index}
                      className={`flex ${msg.isFromUser ? "justify-end" : "justify-start"}`}
                    >
                      <div
                        className={`max-w-xs px-3 py-2 rounded-lg text-sm ${
                          msg.isFromUser
                            ? "bg-[#F52D2A] text-white"
                            : "bg-white text-gray-700 border border-gray-200"
                        }`}
                      >
                        <p className="whitespace-pre-wrap">{msg.text}</p>
                        <p className="text-xs opacity-70 mt-1">
                          {new Date(msg.createdAt).toLocaleTimeString([], {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}
          </div>

          {/* Message Input - Only show when chat is started */}
          {isStarted && (
            <div className="p-4 border-t border-gray-200">
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message..."
                  disabled={isSending}
                  className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 disabled:opacity-50"
                />
                <button
                  onClick={handleSendMessage}
                  disabled={!message.trim() || isSending}
                  className="p-2 rounded-md hover:opacity-90 transition-opacity duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{ backgroundColor: "#F52D2A" }}
                >
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z" />
                  </svg>
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Chat Button */}
      {!isOpen && (
        <div className="fixed bottom-6 right-6 z-50">
          <button
            onClick={handleChatClick}
            className="text-white text-sm font-medium px-4 py-3 rounded-full shadow-lg hover:opacity-90 transition-opacity duration-200 flex items-center space-x-2"
            style={{ backgroundColor: "#F52D2A" }}
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z" />
            </svg>
            <span>CLICK HERE TO CHAT</span>
          </button>
        </div>
      )}
    </>
  );
};

export default ChatWidget;
