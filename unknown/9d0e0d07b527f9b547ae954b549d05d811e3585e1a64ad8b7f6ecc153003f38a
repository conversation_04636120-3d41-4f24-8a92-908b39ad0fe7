import { useSDK } from "@/hooks/useSDK";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useAdminListingActions = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();

  const updateListingStatusMutation = useMutation({
    mutationFn: ({
      listingId,
      status,
      reportId,
    }: {
      listingId: number;
      status: string;
      reportId?: number;
    }) =>
      sdk.callCustomAPI(
        `/v1/api/ebadollar/custom/admin/listing/${listingId}/status`,
        "PUT",
        {
          status,
          report_id: reportId,
        }
      ),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["reportedListings"] });
    },
  });

  const deleteListingMutation = useMutation({
    mutationFn: ({
      listingId,
      reportId,
    }: {
      listingId: number;
      reportId?: number;
    }) =>
      sdk.callCustomAPI(
        `/v1/api/ebadollar/custom/admin/listing/${listingId}`,
        "DELETE",
        {
          report_id: reportId,
        }
      ),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["reportedListings"] });
    },
  });

  return {
    updateListingStatus: updateListingStatusMutation.mutateAsync,
    deleteListing: deleteListingMutation.mutateAsync,
    isLoading:
      updateListingStatusMutation.isPending || deleteListingMutation.isPending,
  };
};
