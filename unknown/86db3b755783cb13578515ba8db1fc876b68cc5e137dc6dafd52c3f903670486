import React, { useState } from "react";
import InteractiveButton from "@/components/InteractiveButton/InteractiveButton";
import StripePaymentModal from "@/components/StripePaymentModal/StripePaymentModal";

interface TopUpRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit?: (data: any) => void;
  onStripeSuccess?: (result: any) => void;
}

type PaymentMethod =
  | "paypal"
  | "bank_transfer"
  | "credit_debit"
  | "etransfer"
  | "local_wire";

const TopUpRequestModal: React.FC<TopUpRequestModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  onStripeSuccess,
}) => {
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod>("paypal");
  const [senderName, setSenderName] = useState("Alex Johnson");
  const [senderEmail, setSenderEmail] = useState("<EMAIL>");
  const [amount, setAmount] = useState("100.00");
  const [reference, setReference] = useState("");
  const [dateOfTransfer, setDateOfTransfer] = useState("");
  const [notes, setNotes] = useState("");
  const [showStripeModal, setShowStripeModal] = useState(false);

  if (!isOpen) return null;

  const handleSubmit = () => {
    // For credit/debit card, show Stripe modal instead
    if (selectedMethod === "credit_debit") {
      setShowStripeModal(true);
      return;
    }

    // For other payment methods, use the regular flow
    const data = {
      method: selectedMethod,
      amount: parseFloat(amount),
      reference,
      notes,
      sender_name: senderName,
      sender_email: senderEmail,
      date_of_transfer: dateOfTransfer,
    };

    if (onSubmit) {
      onSubmit(data);
    }
  };

  const handleStripeSuccess = (result: any) => {
    setShowStripeModal(false);
    if (onStripeSuccess) {
      onStripeSuccess(result);
    }
    onClose(); // Close the main modal after successful payment
  };

  const renderPaymentMethodContent = () => {
    switch (selectedMethod) {
      case "paypal":
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sender Name (optional if same as user)
              </label>
              <input
                type="text"
                value={senderName}
                onChange={(e) => setSenderName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sender Email or Phone Number
              </label>
              <input
                type="text"
                value={senderEmail}
                onChange={(e) => setSenderEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Amount to Add
              </label>
              <div className="relative">
                <span className="absolute left-3 top-2 text-gray-500 text-sm">
                  $
                </span>
                <input
                  type="text"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Reference / Transaction ID
              </label>
              <input
                type="text"
                placeholder="e.g., PayPal transaction ID or bank reference"
                value={reference}
                onChange={(e) => setReference(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date of Transfer
              </label>
              <input
                type="date"
                value={dateOfTransfer}
                onChange={(e) => setDateOfTransfer(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Optional Notes
              </label>
              <textarea
                placeholder="e.g., Sent from XYZ Bank"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm resize-none"
              />
            </div>
          </div>
        );

      case "bank_transfer":
        return (
          <div className="space-y-4">
            {/* Bank Transfer Instructions */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start space-x-2">
                <span className="text-yellow-600 text-sm">⚠️</span>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">
                    Bank Transfer Instructions:
                  </h4>
                  <div className="text-sm text-gray-700 space-y-1">
                    <p>
                      Please transfer funds to the bank account listed in your
                      account or admin instructions.
                    </p>
                    <p>
                      Include your registered email in the bank note for
                      verification.
                    </p>
                    <p>
                      Upload transaction details below to complete your request.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sender Name (optional if same as user)
              </label>
              <input
                type="text"
                value={senderName}
                onChange={(e) => setSenderName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sender Email or Phone Number
              </label>
              <input
                type="text"
                value={senderEmail}
                onChange={(e) => setSenderEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Amount to Add
              </label>
              <div className="relative">
                <span className="absolute left-3 top-2 text-gray-500 text-sm">
                  $
                </span>
                <input
                  type="text"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Reference / Transaction ID
              </label>
              <input
                type="text"
                placeholder="e.g., Bank transfer reference or confirmation number"
                value={reference}
                onChange={(e) => setReference(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date of Transfer
              </label>
              <input
                type="date"
                value={dateOfTransfer}
                onChange={(e) => setDateOfTransfer(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Optional Notes
              </label>
              <textarea
                placeholder="e.g., Sent from XYZ Bank"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm resize-none"
              />
            </div>
          </div>
        );

      case "credit_debit":
        return (
          <div className="space-y-4">
            {/* Credit/Debit Card Instructions */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-2">
                <span className="text-blue-600 text-sm">💳</span>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">
                    Credit/Debit Card Top-Up:
                  </h4>
                  <div className="text-sm text-gray-700 space-y-1">
                    <p>Your default card on file will be charged instantly.</p>
                    <p>
                      The funds will be added to your prepaid balance
                      immediately upon successful transaction.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Amount to Add
              </label>
              <div className="relative">
                <span className="absolute left-3 top-2 text-gray-500 text-sm">
                  $
                </span>
                <input
                  type="text"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Optional Notes
              </label>
              <textarea
                placeholder="Add any notes (optional)"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm resize-none"
              />
            </div>
          </div>
        );

      case "etransfer":
        return (
          <div className="space-y-4">
            {/* e-Transfer Instructions */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-2">
                <span className="text-blue-600 text-sm">📱</span>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">
                    e-Transfer Instructions:
                  </h4>
                  <div className="text-sm text-gray-700 space-y-1">
                    <p>Send your e-transfer to: <EMAIL></p>
                    <p>
                      Make sure to include your registered email in the
                      message/memo section.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sender Name
              </label>
              <input
                type="text"
                value={senderName}
                onChange={(e) => setSenderName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sender Email or Phone Number
              </label>
              <input
                type="text"
                value={senderEmail}
                onChange={(e) => setSenderEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Amount to Add
              </label>
              <div className="relative">
                <span className="absolute left-3 top-2 text-gray-500 text-sm">
                  $
                </span>
                <input
                  type="text"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                e-Transfer Confirmation Number
              </label>
              <input
                type="text"
                value={reference}
                onChange={(e) => setReference(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date of Transfer
              </label>
              <input
                type="date"
                value={dateOfTransfer}
                onChange={(e) => setDateOfTransfer(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Optional Notes
              </label>
              <textarea
                placeholder="Add any notes (optional)"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm resize-none"
              />
            </div>
          </div>
        );

      case "local_wire":
        return (
          <div className="space-y-4">
            {/* Local Wire Transfer Instructions */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-2">
                <span className="text-blue-600 text-sm">🏛️</span>
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-2">
                    Jamaica Local Wire Transfer Details:
                  </h4>
                  <div className="text-sm text-gray-700 space-y-1">
                    <p>Bank: National Commercial Bank Jamaica</p>
                    <p>Account Name: EBa Platform Ltd</p>
                    <p>Account Number: *********</p>
                    <p>Branch: Half Way Tree</p>
                    <p>Bank Code: 00045</p>
                  </div>
                  <div className="flex items-center space-x-1 mt-2">
                    <span className="text-yellow-600 text-sm">⚠️</span>
                    <p className="text-sm text-gray-700">
                      Use your registered email in the transfer notes.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sender Name
              </label>
              <input
                type="text"
                value={senderName}
                onChange={(e) => setSenderName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Sender Email or Phone Number
              </label>
              <input
                type="text"
                value={senderEmail}
                onChange={(e) => setSenderEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Amount to Add
              </label>
              <div className="relative">
                <span className="absolute left-3 top-2 text-gray-500 text-sm">
                  $
                </span>
                <input
                  type="text"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Reference Number
              </label>
              <input
                type="text"
                placeholder="e.g., NCB********* or JMM8 ref code"
                value={reference}
                onChange={(e) => setReference(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Date of Transfer
              </label>
              <input
                type="date"
                value={dateOfTransfer}
                onChange={(e) => setDateOfTransfer(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Optional Notes
              </label>
              <textarea
                placeholder="e.g., Sent from NCB Mobile App"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm resize-none"
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              Submit Top-Up Request
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-xl"
            >
              ×
            </button>
          </div>

          <div className="p-6">
            {/* Payment Method Selection */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Payment Method
              </h3>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="paypal"
                    checked={selectedMethod === "paypal"}
                    onChange={(e) =>
                      setSelectedMethod(e.target.value as PaymentMethod)
                    }
                    className="mr-3 text-[#0F2C59] focus:ring-[#0F2C59]"
                  />
                  <span className="text-blue-600 mr-2">💳</span>
                  <span className="text-sm text-gray-900">PayPal</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="bank_transfer"
                    checked={selectedMethod === "bank_transfer"}
                    onChange={(e) =>
                      setSelectedMethod(e.target.value as PaymentMethod)
                    }
                    className="mr-3 text-[#0F2C59] focus:ring-[#0F2C59]"
                  />
                  <span className="text-blue-600 mr-2">🏦</span>
                  <span className="text-sm text-gray-900">Bank Transfer</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="credit_debit"
                    checked={selectedMethod === "credit_debit"}
                    onChange={(e) =>
                      setSelectedMethod(e.target.value as PaymentMethod)
                    }
                    className="mr-3 text-[#0F2C59] focus:ring-[#0F2C59]"
                  />
                  <span className="text-blue-600 mr-2">💳</span>
                  <span className="text-sm text-gray-900">
                    Credit/Debit Card
                  </span>
                </label>

                <label className="flex items-center">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="etransfer"
                    checked={selectedMethod === "etransfer"}
                    onChange={(e) =>
                      setSelectedMethod(e.target.value as PaymentMethod)
                    }
                    className="mr-3 text-[#0F2C59] focus:ring-[#0F2C59]"
                  />
                  <span className="text-blue-600 mr-2">📱</span>
                  <span className="text-sm text-gray-900">e-Transfer</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="local_wire"
                    checked={selectedMethod === "local_wire"}
                    onChange={(e) =>
                      setSelectedMethod(e.target.value as PaymentMethod)
                    }
                    className="mr-3 text-[#0F2C59] focus:ring-[#0F2C59]"
                  />
                  <span className="text-blue-600 mr-2">🏛️</span>
                  <span className="text-sm text-gray-900">
                    Local Wire Transfer (Jamaica)
                  </span>
                </label>
              </div>
            </div>

            {/* Dynamic Content Based on Payment Method */}
            {renderPaymentMethodContent()}

            {/* Submit Button */}
            <div className="mt-6">
              <InteractiveButton
                onClick={handleSubmit}
                className="w-full bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white py-3 rounded-md font-medium"
              >
                {selectedMethod === "credit_debit"
                  ? "Top-Up Now"
                  : "Submit Top-Up Request"}
              </InteractiveButton>
            </div>
          </div>
        </div>
      </div>

      {/* Stripe Payment Modal for Top-Up */}
      <StripePaymentModal
        isOpen={showStripeModal}
        onClose={() => setShowStripeModal(false)}
        onSuccess={handleStripeSuccess}
        mode="process_payment"
        amount={parseFloat(amount)}
        currency="usd"
      />
    </>
  );
};

export default TopUpRequestModal;
