import React, { useEffect, useState } from "react";
import { AdminWrapper } from "../../../components/AdminWrapper";
import { InteractiveButton } from "../../../components/InteractiveButton";
import { useNavigate } from "react-router-dom";
import { useSDK } from "../../../hooks/useSDK";
import { MkdLoader } from "../../../components/MkdLoader";
import { PaginationBar } from "../../../components/PaginationBar";
import { showToast, GlobalContext } from "../../../context/Global";
import { MkdInputV2 } from "../../../components/MkdInputV2";
import SearchIcon from "../../../assets/svgs/SearchIcon";
import emptyPageImg from "@/assets/images/empty-page.png";

interface IComplaint {
  id: number;
  agentName: string;
  agentPhoto: string | null;
  totalDeliveries: number;
  totalComplaints: number;
  agentResponses: number;
  complaintRate: string;
  responseRate: string;
}

const AdminDeliveryAgentComplaintsListPage = () => {
  const navigate = useNavigate();
  const { sdk } = useSDK();
  const { dispatch } = React.useContext(GlobalContext);

  const [complaints, setComplaints] = useState<IComplaint[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<{
    page: number;
    limit: number;
    total: number;
    num_pages: number;
  } | null>(null);
  const [filters, setFilters] = useState({
    page: 1,
    limit: 10,
    sort_by: "agent_name",
    sort_order: "asc",
    agent_name: "",
    status: "all",
  });

  const statusOptions = [
    { value: "all", label: "All Status" },
    { value: "open", label: "Open" },
    { value: "closed", label: "Closed" },
    { value: "resolved", label: "Resolved" },
  ];

  const sortOptions = [
    { value: "agent_name,asc", label: "Sort by: Name (A-Z)" },
    { value: "agent_name,desc", label: "Sort by: Name (Z-A)" },
    { value: "total_deliveries,desc", label: "Sort by: Most Deliveries" },
    { value: "total_complaints,desc", label: "Sort by: Most Complaints" },
    { value: "complaint_rate,desc", label: "Sort by: Highest Complaint Rate" },
    { value: "response_rate,asc", label: "Sort by: Lowest Response Rate" },
  ];

  const fetchComplaints = async () => {
    setLoading(true);
    try {
      const result = await sdk.callRawAPI(
        "/v2/api/ebadollar/custom/admin/delivery-complaints",
        filters,
        "GET"
      );
      if (!result.error) {
        setComplaints(result.data);
        setPagination(result.pagination);
      } else {
        showToast(dispatch, result.message || "Error fetching complaints");
      }
    } catch (error) {
      console.error("Error fetching complaints", error);
      showToast(dispatch, "Error fetching complaints");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchComplaints();
  }, [filters]);

  const handleFilterChange = (key: string, value: string | number) => {
    setFilters((prev) => ({ ...prev, [key]: value, page: 1 }));
  };

  return (
    <AdminWrapper>
      <div className="p-6 bg-gray-50 min-h-screen">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-3xl font-bold text-[#1E293B]">
              Delivery Agent Complaints
            </h1>
            <p className="text-gray-500 mt-1">
              Monitor and evaluate delivery agent performance and complaint
              handling
            </p>
          </div>
        </div>

        <div className="mb-6 p-4 bg-white rounded-lg shadow">
          <div className="flex items-end gap-4">
            <div className=" flex-[2]">
              <MkdInputV2
                placeholder="Search agent name..."
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  handleFilterChange("agent_name", e.target.value)
                }
              >
                <div className="relative">
                  <MkdInputV2.Field className="!border !border-[#D1D5DB] pl-10 !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59]" />
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <SearchIcon className="h-5 w-5 text-gray-400" />
                  </div>
                </div>
              </MkdInputV2>
            </div>
            <div className="flex-[1]">
              <MkdInputV2
                type="mapping"
                mapping={statusOptions.reduce(
                  (acc, curr) => ({ ...acc, [curr.value]: curr.label }),
                  {}
                )}
                value={filters.status}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                  handleFilterChange("status", e.target.value)
                }
              >
                <MkdInputV2.Container>
                  <MkdInputV2.Field className="!border !border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59]" />
                </MkdInputV2.Container>
              </MkdInputV2>
            </div>
            <div className="flex-[1]">
              <MkdInputV2
                type="mapping"
                mapping={sortOptions.reduce(
                  (acc, curr) => ({ ...acc, [curr.value]: curr.label }),
                  {}
                )}
                value={`${filters.sort_by},${filters.sort_order}`}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                  const [sort_by, sort_order] = e.target.value.split(",");
                  setFilters((prev) => ({ ...prev, sort_by, sort_order }));
                }}
              >
                <MkdInputV2.Container>
                  <MkdInputV2.Field className="!border !border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59]" />
                </MkdInputV2.Container>
              </MkdInputV2>
            </div>
            <InteractiveButton
              className="!bg-[#0F2C59] !text-white !h-full !py-4 !px-4 !rounded-lg"
              onClick={fetchComplaints}
            >
              Apply filters
            </InteractiveButton>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow overflow-hidden">
          {loading ? (
            <MkdLoader />
          ) : (
            <>
              <table className="min-w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="p-4 text-left text-sm font-medium text-gray-500">
                      <input
                        type="checkbox"
                        className="h-4 w-4 !border-[#D1D5DB] !focus:ring-[#0F2C59]"
                      />
                    </th>
                    <th className="p-4 text-left text-sm font-medium text-gray-500">
                      Agent Name
                    </th>
                    <th className="p-4 text-left text-sm font-medium text-gray-500">
                      Total Deliveries
                    </th>
                    <th className="p-4 text-left text-sm font-medium text-gray-500">
                      Total Complaints
                    </th>
                    <th className="p-4 text-left text-sm font-medium text-gray-500">
                      Agent Responses
                    </th>
                    <th className="p-4 text-left text-sm font-medium text-gray-500">
                      Complaint Rate
                    </th>
                    <th className="p-4 text-left text-sm font-medium text-gray-500">
                      Response Rate
                    </th>
                    <th className="p-4 text-left text-sm font-medium text-gray-500">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {complaints.length === 0 ? (
                    <tr>
                      <td colSpan={7}>
                        <div className="flex flex-col items-center justify-center py-10">
                          <div className="mb-4 ">
                            <img
                              src={emptyPageImg}
                              alt="No records"
                              className="w-full h-full"
                            />
                          </div>
                          <div className="text-gray-500 text-lg font-medium">
                            No records found.
                          </div>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    complaints.map((item) => (
                      <tr key={item.id}>
                        <td className="p-4">
                          <input
                            type="checkbox"
                            className="h-4 w-4 !border-[#D1D5DB] !focus:ring-[#0F2C59]"
                          />
                        </td>
                        <td className="p-4 flex items-center">
                          <img
                            src={
                              item.agentPhoto ||
                              `https://i.pravatar.cc/40?u=${item.agentName}`
                            }
                            alt={item.agentName}
                            className="w-8 h-8 rounded-full mr-3"
                          />
                          <span>{item.agentName}</span>
                        </td>
                        <td className="p-4">{item.totalDeliveries}</td>
                        <td className="p-4">{item.totalComplaints}</td>
                        <td className="p-4">{item.agentResponses}</td>
                        <td className="p-4">
                          <span
                            className={`px-2 py-1 rounded-full text-xs ${
                              parseFloat(item.complaintRate) > 10
                                ? "bg-red-100 text-[#E63946]"
                                : "bg-green-100 text-green-800"
                            }`}
                          >
                            {item.complaintRate}
                          </span>
                        </td>
                        <td className="p-4">
                          <span
                            className={`px-2 py-1 rounded-full text-xs ${
                              parseFloat(item.responseRate) < 80
                                ? "bg-red-100 text-[#E63946]"
                                : "bg-green-100 text-green-800"
                            }`}
                          >
                            {item.responseRate}
                          </span>
                        </td>
                        <td className="p-4">
                          <button
                            onClick={() =>
                              navigate(
                                `/admin/delivery-agent-complaints/${item.id}`
                              )
                            }
                            className="text-blue-600 hover:underline"
                          >
                            View
                          </button>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
              {pagination && complaints.length > 0 && (
                <PaginationBar
                  currentPage={pagination.page}
                  pageCount={pagination.num_pages}
                  pageSize={pagination.limit}
                  canPreviousPage={pagination.page > 1}
                  canNextPage={pagination.page < pagination.num_pages}
                  updatePageSize={(size) => handleFilterChange("limit", size)}
                  updateCurrentPage={(page) => handleFilterChange("page", page)}
                  canChangeLimit={true}
                  startSize={10}
                  multiplier={10}
                />
              )}
            </>
          )}
        </div>
      </div>
    </AdminWrapper>
  );
};

export default AdminDeliveryAgentComplaintsListPage;
