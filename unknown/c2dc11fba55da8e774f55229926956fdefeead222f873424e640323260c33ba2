import React, { useState } from "react";
import { MemberWrapper } from "../../components/MemberWrapper";
import { useVerifyPickupCodeMutation, useVerifyDeliveryCodeMutation } from "../../query/useConfirmationCodes";

interface ICodeVerificationForm {
  assignmentId: string;
  code: string;
  type: "pickup" | "delivery";
}

const ConfirmationCodeVerificationPage: React.FC = () => {
  const [form, setForm] = useState<ICodeVerificationForm>({
    assignmentId: "",
    code: "",
    type: "pickup",
  });
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string>("");

  const verifyPickupCodeMutation = useVerifyPickupCodeMutation();
  const verifyDeliveryCodeMutation = useVerifyDeliveryCodeMutation();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setForm(prev => ({ ...prev, [name]: value }));
    // Clear messages when user starts typing
    setSuccessMessage("");
    setErrorMessage("");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!form.assignmentId || !form.code) {
      setErrorMessage("Please fill in all fields");
      return;
    }

    const assignmentId = parseInt(form.assignmentId);
    if (isNaN(assignmentId)) {
      setErrorMessage("Assignment ID must be a valid number");
      return;
    }

    try {
      if (form.type === "pickup") {
        await verifyPickupCodeMutation.mutateAsync({
          assignmentId,
          pickupCode: form.code,
        });
        setSuccessMessage("Pickup code verified successfully! Package marked as picked up.");
      } else {
        await verifyDeliveryCodeMutation.mutateAsync({
          assignmentId,
          deliveryCode: form.code,
        });
        setSuccessMessage("Delivery code verified successfully! Package marked as delivered.");
      }
      
      // Reset form
      setForm({
        assignmentId: "",
        code: "",
        type: "pickup",
      });
    } catch (error: any) {
      setErrorMessage(error.message || "Failed to verify code");
    }
  };

  const isLoading = verifyPickupCodeMutation.isPending || verifyDeliveryCodeMutation.isPending;

  return (
    <MemberWrapper>
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            Confirmation Code Verification
          </h1>
          
          <p className="text-gray-600 mb-6">
            Enter the confirmation code provided by the seller (for pickup) or buyer (for delivery) 
            to verify the transaction step.
          </p>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Code Type Selection */}
            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
                Code Type
              </label>
              <select
                id="type"
                name="type"
                value={form.type}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0D3166] focus:border-transparent"
              >
                <option value="pickup">Pickup Code (from Seller)</option>
                <option value="delivery">Delivery Code (from Buyer)</option>
              </select>
            </div>

            {/* Assignment ID */}
            <div>
              <label htmlFor="assignmentId" className="block text-sm font-medium text-gray-700 mb-2">
                Assignment ID
              </label>
              <input
                type="text"
                id="assignmentId"
                name="assignmentId"
                value={form.assignmentId}
                onChange={handleInputChange}
                placeholder="Enter your delivery assignment ID"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0D3166] focus:border-transparent"
              />
            </div>

            {/* Confirmation Code */}
            <div>
              <label htmlFor="code" className="block text-sm font-medium text-gray-700 mb-2">
                Confirmation Code
              </label>
              <input
                type="text"
                id="code"
                name="code"
                value={form.code}
                onChange={handleInputChange}
                placeholder="Enter 6-character confirmation code"
                maxLength={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0D3166] focus:border-transparent font-mono text-lg tracking-wider"
                style={{ textTransform: 'uppercase' }}
              />
            </div>

            {/* Success Message */}
            {successMessage && (
              <div className="bg-green-50 border border-green-200 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-green-800">
                      {successMessage}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Error Message */}
            {errorMessage && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-red-800">
                      {errorMessage}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-[#0D3166] text-white py-3 px-4 rounded-md hover:bg-[#1a4480] focus:outline-none focus:ring-2 focus:ring-[#0D3166] focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Verifying...
                </div>
              ) : (
                `Verify ${form.type === "pickup" ? "Pickup" : "Delivery"} Code`
              )}
            </button>
          </form>

          {/* Instructions */}
          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h3 className="text-sm font-medium text-blue-900 mb-2">Instructions:</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• <strong>Pickup Code:</strong> Get this from the seller when collecting the item</li>
              <li>• <strong>Delivery Code:</strong> Get this from the buyer when delivering the item</li>
              <li>• Codes are 6 characters long and case-insensitive</li>
              <li>• Each code can only be used once</li>
              <li>• Pickup must be verified before delivery</li>
            </ul>
          </div>
        </div>
      </div>
    </MemberWrapper>
  );
};

export default ConfirmationCodeVerificationPage;
