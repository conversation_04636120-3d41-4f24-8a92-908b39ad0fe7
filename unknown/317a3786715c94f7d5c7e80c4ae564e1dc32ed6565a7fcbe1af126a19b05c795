import { useEffect, useState } from "react";
import { useSDK } from "@/hooks/useSDK";
import { useParams } from "react-router-dom";

interface IShipping {
  method: string;
  tracking: string;
  location: string;
}

interface IListingDetails {
  status: string;
  title: string;
  category: string;
  price: string;
  description: string;
  images: string[];
  shipping: IShipping;
  seller: {
    photo?: string;
  };
}

export const useListingDetails = () => {
  const { sdk } = useSDK();
  const { id } = useParams();
  const [listingDetails, setListingDetails] = useState<IListingDetails | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchListingDetails = async () => {
      try {
        setLoading(true);
        const response = await sdk.callRawAPI(
          `/v2/api/ebadollar/admin/listings/${id}`,
          {},
          "GET"
        );
        if (!response.error) {
          setListingDetails(response.data);
        } else {
          setError(new Error(response.message));
        }
      } catch (err: unknown) {
        setError(
          err instanceof Error ? err : new Error("An unknown error occurred.")
        );
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchListingDetails();
    }
  }, [id]);

  return { listingDetails, loading, error };
};
