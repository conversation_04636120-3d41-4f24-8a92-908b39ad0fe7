import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useSDK } from "../hooks/useSDK";
import { useToast } from "../hooks/useToast";
import { ToastStatusEnum } from "../utils/Enums";

// Query to get promotion settings
export const usePromotionSettingsQuery = () => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["promotionSettings"],
    queryFn: async () => {
      console.log("🚀 Fetching promotion settings...");
      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/admin/promotion/settings",
          method: "GET",
        });
        console.log("📡 Promotion settings API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Promotion settings API Call Error:", error);
        throw error;
      }
    },
  });
};

// Query to get sponsored listings with pagination
export const useSponsoredListingsQuery = (page = 1, limit = 10) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["sponsoredListings", page, limit],
    queryFn: async () => {
      console.log(
        "🚀 Fetching sponsored listings with page:",
        page,
        "limit:",
        limit
      );
      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/admin/sponsored-listings`,
          method: "GET",
          params: {
            page: page.toString(),
            limit: limit.toString(),
          },
        });
        console.log("📡 Sponsored listings API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Sponsored listings API Call Error:", error);
        throw error;
      }
    },
  });
};

// Mutation to update promotion settings
export const useUpdatePromotionSettingsMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: async (data: any) => {
      console.log("🚀 [MUTATION] Updating promotion settings with data:", data);
      console.log("🔧 [MUTATION] SDK instance:", sdk);
      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/admin/promotion/settings",
          method: "PUT",
          body: data,
        });
        console.log(
          "📡 [MUTATION] Update promotion settings API Response:",
          response
        );
        return response;
      } catch (error) {
        console.error(
          "❌ [MUTATION] Update promotion settings API Call Error:",
          error
        );
        throw error;
      }
    },
    onSuccess: (res: any) => {
      console.log("✅ Update Promotion Settings Mutation Success:", res);
      if (!res.error) {
        showToast(
          "Promotion settings updated successfully!",
          5000,
          ToastStatusEnum.SUCCESS
        );
        queryClient.invalidateQueries({ queryKey: ["promotionSettings"] });
      } else {
        console.error("❌ API returned error:", res.message);
        showToast(
          res.message || "Failed to update promotion settings",
          5000,
          ToastStatusEnum.ERROR
        );
      }
    },
    onError: (err: any) => {
      console.error("❌ Update Promotion Settings Mutation Error:", err);
      showToast(
        err.message || "An error occurred while updating promotion settings",
        5000,
        ToastStatusEnum.ERROR
      );
    },
  });
};

// Mutation to delete sponsored listing
export const useDeleteSponsoredListingMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: async (id: number) => {
      console.log(
        "🚀 [DELETE MUTATION] Deleting sponsored listing with id:",
        id
      );
      console.log("🔧 [DELETE MUTATION] SDK instance:", sdk);
      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/admin/sponsored-listings/${id}`,
          method: "DELETE",
        });
        console.log(
          "📡 [DELETE MUTATION] Delete sponsored listing API Response:",
          response
        );
        return response;
      } catch (error) {
        console.error(
          "❌ [DELETE MUTATION] Delete sponsored listing API Call Error:",
          error
        );
        throw error;
      }
    },
    onSuccess: (res: any) => {
      console.log("✅ Delete Sponsored Listing Mutation Success:", res);
      if (!res.error) {
        showToast(
          "Sponsored listing deleted successfully!",
          5000,
          ToastStatusEnum.SUCCESS
        );
        queryClient.invalidateQueries({ queryKey: ["sponsoredListings"] });
      } else {
        console.error("❌ API returned error:", res.message);
        showToast(
          res.message || "Failed to delete sponsored listing",
          5000,
          ToastStatusEnum.ERROR
        );
      }
    },
    onError: (err: any) => {
      console.error("❌ Delete Sponsored Listing Mutation Error:", err);
      showToast(
        err.message || "An error occurred while deleting sponsored listing",
        5000,
        ToastStatusEnum.ERROR
      );
    },
  });
};

// Mutation to promote a listing
export const usePromoteListingMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: async (data: { listing_id: number; days: number }) => {
      console.log("🚀 [PROMOTE MUTATION] Promoting listing with data:", data);
      console.log("🔧 [PROMOTE MUTATION] SDK instance:", sdk);
      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/admin/promote-listing",
          method: "POST",
          body: data,
        });
        console.log(
          "📡 [PROMOTE MUTATION] Promote listing API Response:",
          response
        );
        return response;
      } catch (error) {
        console.error(
          "❌ [PROMOTE MUTATION] Promote listing API Call Error:",
          error
        );
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log("✅ [PROMOTE MUTATION] Success:", data);
      showToast(
        "Listing promoted successfully!",
        5000,
        ToastStatusEnum.SUCCESS
      );
      // Invalidate and refetch listings data
      queryClient.invalidateQueries({ queryKey: ["listings"] });
    },
    onError: (error: any) => {
      console.error("❌ [PROMOTE MUTATION] Error:", error);
      showToast(
        error?.message || "Failed to promote listing. Please try again.",
        5000,
        ToastStatusEnum.ERROR
      );
    },
  });
};
