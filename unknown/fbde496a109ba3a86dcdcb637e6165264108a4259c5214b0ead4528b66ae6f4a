import { AdminReportedListingListPageProps } from "./AdminReportedListingListPage.types";

export const filters = [
  { label: "All Reports", value: "all" },
  { label: "Last 7 days", value: "7d" },
  { label: "Last 30 days", value: "30d" },
];

export const sorts = [
  { label: "Sort by: Newest", value: "newest" },
  { label: "Sort by: Oldest", value: "oldest" },
];

export const listings = [
  {
    id: "#2123",
    title: "iPhone 15 Pro Max",
    seller: "John Doe",
    reports: 25,
    status: "republish",
  },
  {
    id: "#2124",
    title: "Samsung Galaxy S24",
    seller: "<PERSON>",
    reports: 15,
    status: "hide",
  },
  {
    id: "#2125",
    title: 'MacBook Pro 16"',
    seller: "<PERSON>",
    reports: 8,
    status: "hide",
  },
  {
    id: "#2126",
    title: "iPad Air 5th Gen",
    seller: "<PERSON> Wilson",
    reports: 3,
    status: "hide",
  },
  {
    id: "#2127",
    title: "AirPods Pro 2",
    seller: "<PERSON>",
    reports: 32,
    status: "republish",
  },
];
