import { useSDK } from "../../hooks/useSDK";
import { useContexts } from "../../hooks/useContexts";
import { useState, useEffect, useCallback } from "react";
import { useParams } from "react-router-dom";

interface IComplaintDetails {
  id: string;
  status: string;
  date: string;
  listing: {
    id: string;
    title: string;
    image: string | null;
  };
  complainant: {
    id: string;
    name: string;
    email: string;
    image: string | null;
  };
  agent: {
    id: string;
    name: string;
    rating: number;
    total_complaints: number;
    avatar: string | null;
  };
  rating: number;
  complaint_summary: string;
  agent_response: string;
  is_actionable: boolean;
}

export const useDeliveryAgentComplaintDetails = () => {
  const { sdk } = useSDK();
  const { id: agent_id } = useParams(); // This is the agent ID from the route
  const { globalDispatch } = useContexts();

  const [complaints, setComplaints] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!agent_id) {
      setError(new Error("Agent ID is required"));
      setLoading(false);
      return;
    }

    let isMounted = true; // Flag to prevent state updates after unmount

    const fetchComplaints = async () => {
      if (!isMounted) return;

      setLoading(true);
      setError(null);
      try {
        const result = await sdk.callRawAPI(
          `/v2/api/ebadollar/custom/admin/delivery-agent-complaints/${agent_id}`,
          {},
          "GET"
        );
        if (!isMounted) return; // Check again after async operation

        if (!result.error) {
          setComplaints(result.data || []);
        } else {
          throw new Error(result.message || "Failed to fetch complaints");
        }
      } catch (err: any) {
        if (isMounted) {
          // Provide more user-friendly error messages
          let errorMessage = "Failed to fetch agent complaints";

          if (
            err.message?.includes("ECONNRESET") ||
            err.message?.includes("connection")
          ) {
            errorMessage =
              "Database connection issue. Please try refreshing the page.";
          } else if (err.message?.includes("timeout")) {
            errorMessage = "Request timed out. Please try again.";
          } else if (err.message) {
            errorMessage = err.message;
          }

          setError(new Error(errorMessage));
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchComplaints();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, [agent_id]); // Only depend on agent_id

  const refetchComplaints = useCallback(async () => {
    if (!agent_id) return;

    setLoading(true);
    setError(null);
    try {
      const result = await sdk.callRawAPI(
        `/v2/api/ebadollar/custom/admin/delivery-agent-complaints/${agent_id}`,
        {},
        "GET"
      );
      if (!result.error) {
        setComplaints(result.data || []);
      } else {
        throw new Error(result.message || "Failed to fetch complaints");
      }
    } catch (err: any) {
      setError(err);
    } finally {
      setLoading(false);
    }
  }, [agent_id]); // Removed sdk dependency

  const updateComplaintStatus = async (
    complaintId: number,
    status: "valid" | "invalid"
  ) => {
    if (!agent_id) {
      globalDispatch({
        type: "SNACKBAR",
        payload: {
          message: "Agent ID is required",
          toastStatus: "error",
        },
      });
      return;
    }

    try {
      const result = await sdk.callRawAPI(
        `/v2/api/ebadollar/custom/admin/delivery-agent-complaints/complaint/${complaintId}/status`,
        { status },
        "PUT"
      );
      if (!result.error) {
        globalDispatch({
          type: "SNACKBAR",
          payload: {
            message: "Status updated successfully",
            toastStatus: "success",
          },
        });

        // Update the specific complaint in the local state
        setComplaints((prev) =>
          prev.map((complaint) =>
            complaint.id === complaintId ? { ...complaint, status } : complaint
          )
        );
      } else {
        throw new Error(result.message || "Failed to update status");
      }
    } catch (err: any) {
      globalDispatch({
        type: "SNACKBAR",
        payload: {
          message: err.message,
          toastStatus: "error",
        },
      });
    }
  };

  const suspendDeliveryAgent = async () => {
    if (!agent_id) {
      globalDispatch({
        type: "SNACKBAR",
        payload: {
          message: "Agent ID is required",
          toastStatus: "error",
        },
      });
      return;
    }

    try {
      const result = await sdk.callRawAPI(
        `/v2/api/ebadollar/custom/admin/users/${agent_id}/status`,
        { status: 2 }, // 2 = Suspend according to user model
        "PUT"
      );
      if (!result.error) {
        globalDispatch({
          type: "SNACKBAR",
          payload: {
            message: "Delivery agent suspended successfully",
            toastStatus: "success",
          },
        });
      } else {
        throw new Error(result.message || "Failed to suspend user");
      }
    } catch (err: any) {
      globalDispatch({
        type: "SNACKBAR",
        payload: {
          message: err.message,
          toastStatus: "error",
        },
      });
    }
  };

  return {
    complaints,
    loading,
    error,
    updateComplaintStatus,
    suspendDeliveryAgent,
    refetchComplaints,
    agentId: agent_id,
  };
};
