import React, { useState, useRef, useCallback } from "react";
import InteractiveButton from "../InteractiveButton/InteractiveButton";
import MicrophoneIcon from "@/assets/svgs/MicrophoneIcon";

interface AudioRecorderProps {
  onRecordingComplete: (audioBlob: Blob) => void;
  onClose: () => void;
}

const AudioRecorder: React.FC<AudioRecorderProps> = ({
  onRecordingComplete,
  onClose,
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<number | null>(null);

  const startRecording = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      chunksRef.current = [];

      mediaRecorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
          chunksRef.current.push(e.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(chunksRef.current, { type: "audio/webm" });
        onRecordingComplete(audioBlob);
        stream.getTracks().forEach((track) => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
      setRecordingTime(0);

      // Start timer
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    } catch (error) {
      console.error("Error accessing microphone:", error);
    }
  }, [onRecordingComplete]);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    }
  }, [isRecording]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            Voice Verification
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            ✕
          </button>
        </div>

        <div className="text-center py-8">
          <div className="mb-4">
            <div
              className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto ${isRecording ? "bg-red-500" : "bg-gray-100"}`}
            >
              <MicrophoneIcon
                className={`w-8 h-8 ${isRecording ? "text-white" : "text-gray-600"}`}
              />
            </div>
            {isRecording && (
              <div className="mt-2 text-red-500 font-medium">
                Recording... {formatTime(recordingTime)}
              </div>
            )}
          </div>

          <p className="text-sm text-gray-600 mb-6">
            {isRecording
              ? "Speak clearly and naturally..."
              : "Click 'Start Recording' and read the following text:"}
          </p>

          {!isRecording && (
            <div className="bg-gray-50 p-4 rounded-lg mb-6">
              <p className="text-gray-700">
                "I verify that I am a real person and this account belongs to
                me."
              </p>
            </div>
          )}
        </div>

        <div className="flex justify-end gap-3">
          <InteractiveButton
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 !text-gray-700 bg-white hover:bg-gray-50 rounded-md"
          >
            Cancel
          </InteractiveButton>
          <InteractiveButton
            type="button"
            onClick={isRecording ? stopRecording : startRecording}
            className={`px-4 py-2 rounded-md ${
              isRecording
                ? "bg-red-500 hover:bg-red-600 !text-white"
                : "bg-[#0F2C59] hover:bg-[#0F2C59]/90 !text-white"
            }`}
          >
            {isRecording ? "Stop Recording" : "Start Recording"}
          </InteractiveButton>
        </div>
      </div>
    </div>
  );
};

export default AudioRecorder;
