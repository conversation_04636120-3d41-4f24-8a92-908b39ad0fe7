import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useSDK } from "../hooks/useSDK";
import { useToast } from "../hooks/useToast";
import { ToastStatusEnum } from "../utils/Enums";

export const useAddCategoryMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: async (data: { name: string }) => {
      console.log("🚀 Creating category with data:", data);
      console.log("🔑 Token from localStorage:", localStorage.getItem("token"));
      console.log(
        "👤 User role from localStorage:",
        localStorage.getItem("role")
      );

      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/admin/categories",
          method: "POST",
          body: data,
        });
        console.log("📡 API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (res: any) => {
      console.log("✅ Mutation Success:", res);
      if (!res.error) {
        showToast(
          "Category created successfully",
          5000,
          ToastStatusEnum.SUCCESS
        );
        queryClient.invalidateQueries({ queryKey: ["categories"] });
      } else {
        console.error("❌ API returned error:", res.message);
        showToast(
          res.message || "Failed to create category",
          5000,
          ToastStatusEnum.ERROR
        );
      }
    },
    onError: (err: any) => {
      console.error("❌ Mutation Error:", err);
      showToast(
        err.message || "An error occurred while creating the category",
        5000,
        ToastStatusEnum.ERROR
      );
    },
  });
};

export const useCategoryQuery = (id: string) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["category", id],
    queryFn: async () => {
      console.log("🚀 Fetching category with ID:", id);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/admin/categories/${id}`,
          method: "GET",
        });
        console.log("📡 Category API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Category API Call Error:", error);
        throw error;
      }
    },
    enabled: !!id,
  });
};

export const useUpdateCategoryMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: { name: string; status: string };
    }) => {
      console.log("🚀 Updating category with ID:", id, "data:", data);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/admin/categories/${id}`,
          method: "PUT",
          body: data,
        });
        console.log("📡 Update Category API Response:", response);
        return response;
      } catch (error) {
        console.error("❌ Update Category API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (res: any) => {
      console.log("✅ Update Category Mutation Success:", res);
      if (!res.error) {
        showToast(
          "Category updated successfully",
          5000,
          ToastStatusEnum.SUCCESS
        );
        queryClient.invalidateQueries({ queryKey: ["categories"] });
        queryClient.invalidateQueries({ queryKey: ["category"] });
      } else {
        console.error("❌ API returned error:", res.message);
        showToast(
          res.message || "Failed to update category",
          5000,
          ToastStatusEnum.ERROR
        );
      }
    },
    onError: (err: any) => {
      console.error("❌ Update Category Mutation Error:", err);
      showToast(
        err.message || "An error occurred while updating the category",
        5000,
        ToastStatusEnum.ERROR
      );
    },
  });
};
