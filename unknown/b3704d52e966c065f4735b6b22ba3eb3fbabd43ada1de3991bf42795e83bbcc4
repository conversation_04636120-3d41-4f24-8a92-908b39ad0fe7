import React from "react";

const TrashIcon = ({ className = "", onClick = () => {} }) => {
  return (
    <svg
      className={`${className}`}
      onClick={onClick}
      width="14"
      height="15"
      viewBox="0 0 14 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_6_685)">
        <g clip-path="url(#clip1_6_685)">
          <path
            d="M4.61875 1.23398L4.42188 1.625H1.79688C1.31289 1.625 0.921875 2.01602 0.921875 2.5C0.921875 2.98398 1.31289 3.375 1.79688 3.375H12.2969C12.7809 3.375 13.1719 2.98398 13.1719 2.5C13.1719 2.01602 12.7809 1.625 12.2969 1.625H9.67188L9.475 1.23398C9.32734 0.935938 9.02383 0.75 8.69297 0.75H5.40078C5.06992 0.75 4.76641 0.935938 4.61875 1.23398ZM12.2969 4.25H1.79688L2.37656 13.5195C2.42031 14.2113 2.99453 14.75 3.68633 14.75H10.4074C11.0992 14.75 11.6734 14.2113 11.7172 13.5195L12.2969 4.25Z"
            fill="#DC2626"
          />
        </g>
      </g>
      <defs>
        <clipPath id="clip0_6_685">
          <rect
            width="12.25"
            height="14"
            fill="white"
            transform="translate(0.921875 0.75)"
          />
        </clipPath>
        <clipPath id="clip1_6_685">
          <path d="M0.921875 0.75H13.1719V14.75H0.921875V0.75Z" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default TrashIcon;
