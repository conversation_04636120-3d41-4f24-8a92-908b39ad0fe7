import {
  useMutation,
  useQueryClient,
  UseMutationOptions,
  InvalidateQueryFilters,
} from "@tanstack/react-query";
import MkdSDK from "../utils/MkdSDK";

export const useCreateUser = () => {
  const queryClient = useQueryClient();
  const sdk = new MkdSDK();

  const mutationOptions: UseMutationOptions<any, Error, any, unknown> = {
    mutationFn: (payload: any) => {
      return sdk.request({
        endpoint: "/v2/api/ebadollar/custom/admin/users",
        method: "POST",
        body: payload,
      });
    },
    onSuccess: () => {
      const filters: InvalidateQueryFilters = { queryKey: ["users"] };
      queryClient.invalidateQueries(filters);
    },
  };

  return useMutation(mutationOptions);
};
