import React, { useEffect, useState } from "react";
import { useSDK } from "../../../hooks/useSDK";
import { Skeleton } from "../../../components/Skeleton";

interface Reference {
  id: number;
  application_id: number;
  name: string;
  relationship: string;
  phone: string;
  created_at: string;
  updated_at: string;
}

interface ReferencesModalProps {
  onClose: () => void;
  applicationId: number;
}

const ReferencesModal = ({ onClose, applicationId }: ReferencesModalProps) => {
  const { sdk } = useSDK();
  const [references, setReferences] = useState<Reference[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchReferences = async () => {
      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/admin/delivery-applications/${applicationId}/references`,
          method: "GET",
        });
        if (!response.error) {
          setReferences(response.data || []);
        }
      } catch (error) {
        console.error("Error fetching references:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchReferences();
  }, [applicationId]);

  if (loading) {
    return (
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-lg font-medium text-gray-900">References *</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-xl font-medium"
          >
            ×
          </button>
        </div>
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  const renderReferenceCard = (reference: Reference, index: number) => (
    <div key={reference.id || index} className="mb-6">
      <h3 className="text-sm font-medium text-gray-900 mb-4">
        Reference {index + 1}
      </h3>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Full Name *
          </label>
          <input
            type="text"
            value={reference.name || ""}
            readOnly
            className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-900"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Relationship *
          </label>
          <input
            type="text"
            value={reference.relationship || ""}
            readOnly
            className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-900"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Phone Number *
          </label>
          <input
            type="text"
            value={reference.phone || ""}
            readOnly
            className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-900"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Email *
          </label>
          <input
            type="email"
            value=""
            placeholder="Not provided"
            readOnly
            className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-500"
          />
        </div>
      </div>
    </div>
  );

  return (
    <div className="bg-white rounded-lg p-6 w-full max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-medium text-gray-900">References *</h2>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 text-xl font-medium"
        >
          ×
        </button>
      </div>

      {/* Dynamic References Layout */}
      {references.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          No references found for this application.
        </div>
      ) : (
        <div
          className={`grid gap-8 ${
            references.length === 1
              ? "grid-cols-1 max-w-md mx-auto"
              : references.length === 2
                ? "grid-cols-2"
                : "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
          }`}
        >
          {references.map((reference, index) =>
            renderReferenceCard(reference, index)
          )}
        </div>
      )}
    </div>
  );
};

export default ReferencesModal;
