import React, { useState, useRef, useCallback } from "react";
import InteractiveButton from "../InteractiveButton/InteractiveButton";
import MicrophoneIcon from "../../assets/svgs/MicrophoneIcon";
import { useToast } from "../../hooks/useToast";

interface VoiceRecorderProps {
  onRecordingComplete?: (audioBlob: Blob) => void;
  onRecordingError?: (error: string) => void;
  maxDuration?: number; // in seconds
  className?: string;
  disabled?: boolean;
}

const VoiceRecorder: React.FC<VoiceRecorderProps> = ({
  onRecordingComplete,
  onRecordingError,
  maxDuration = 60, // 60 seconds default
  className = "",
  disabled = false,
}) => {
  const { success, error: showError } = useToast();
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);
  
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);

  const startRecording = useCallback(async () => {
    try {
      // Request microphone permission
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100,
        }
      });
      
      streamRef.current = stream;
      audioChunksRef.current = [];

      // Create MediaRecorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      mediaRecorderRef.current = mediaRecorder;

      // Handle data available
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      // Handle recording stop
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { 
          type: 'audio/webm;codecs=opus' 
        });
        
        // Create audio URL for preview
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);
        
        if (onRecordingComplete) {
          onRecordingComplete(audioBlob);
        }
        
        // Clean up stream
        if (streamRef.current) {
          streamRef.current.getTracks().forEach(track => track.stop());
          streamRef.current = null;
        }
      };

      // Start recording
      mediaRecorder.start(100); // Collect data every 100ms
      setIsRecording(true);
      setRecordingTime(0);
      
      // Start timer
      const timer = setInterval(() => {
        setRecordingTime(prev => {
          const newTime = prev + 1;
          
          // Auto-stop at max duration
          if (newTime >= maxDuration) {
            stopRecording();
            clearInterval(timer);
            return maxDuration;
          }
          
          return newTime;
        });
      }, 1000);

      success("Recording started");
      
    } catch (error: any) {
      console.error("Error starting recording:", error);
      const errorMessage = error.name === 'NotAllowedError' 
        ? "Microphone permission denied. Please allow microphone access and try again."
        : "Failed to start recording. Please check your microphone.";
      
      showError(errorMessage);
      if (onRecordingError) {
        onRecordingError(errorMessage);
      }
    }
  }, [maxDuration, success, showError, onRecordingComplete, onRecordingError]);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      success("Recording stopped");
    }
  }, [isRecording, success]);

  const toggleRecording = useCallback(() => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  }, [isRecording, startRecording, stopRecording]);

  const formatTime = useCallback((seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }, []);

  const clearRecording = useCallback(() => {
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
      setAudioUrl(null);
    }
    setRecordingTime(0);
  }, [audioUrl]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Recording Controls */}
      <div className="flex items-center justify-center space-x-4">
        <InteractiveButton
          type="button"
          onClick={toggleRecording}
          disabled={disabled}
          className={`px-6 py-3 rounded-full flex items-center space-x-2 font-medium ${
            isRecording 
              ? 'bg-red-500 text-white hover:bg-red-600' 
              : 'bg-blue-500 text-white hover:bg-blue-600'
          }`}
        >
          <MicrophoneIcon className="w-5 h-5" />
          <span>{isRecording ? 'Stop Recording' : 'Start Recording'}</span>
        </InteractiveButton>
        
        {audioUrl && (
          <InteractiveButton
            type="button"
            onClick={clearRecording}
            className="px-4 py-2 bg-gray-500 text-white hover:bg-gray-600 rounded-md text-sm"
          >
            Clear
          </InteractiveButton>
        )}
      </div>

      {/* Recording Status */}
      {(isRecording || recordingTime > 0) && (
        <div className="text-center">
          <div className={`text-lg font-mono ${isRecording ? 'text-red-600' : 'text-gray-600'}`}>
            {formatTime(recordingTime)} / {formatTime(maxDuration)}
          </div>
          {isRecording && (
            <div className="flex items-center justify-center space-x-2 mt-2">
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-red-600">Recording...</span>
            </div>
          )}
        </div>
      )}

      {/* Audio Preview */}
      {audioUrl && (
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Recording Preview</span>
            <span className="text-xs text-gray-500">{formatTime(recordingTime)}</span>
          </div>
          <audio 
            controls 
            src={audioUrl} 
            className="w-full"
            preload="metadata"
          >
            Your browser does not support the audio element.
          </audio>
        </div>
      )}

      {/* Instructions */}
      <div className="text-center text-sm text-gray-500">
        <p>Click "Start Recording" to begin voice verification.</p>
        <p>Maximum recording time: {formatTime(maxDuration)}</p>
      </div>
    </div>
  );
};

export default VoiceRecorder;
