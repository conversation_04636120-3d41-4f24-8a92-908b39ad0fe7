import React, { useState } from "react";
import { Skeleton } from "../../../components/Skeleton";
import { useFAQsQuery } from "../../../query/useLanding";

interface FAQItem {
  question: string;
  answer: string;
}

const FAQSection = () => {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const { data: faqsData, isLoading, error } = useFAQsQuery(); // Get all FAQs

  const faqs = faqsData?.data || [];

  const toggleItem = (index: number) => {
    setOpenItems((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]
    );
  };

  // Use API data or fallback to empty array
  const faqData = faqs.length > 0 ? faqs : [];

  const leftColumnItems = faqData.slice(0, Math.ceil(faqData.length / 2));
  const rightColumnItems = faqData.slice(Math.ceil(faqData.length / 2));

  const FAQItem = ({
    item,
    isOpen,
    onToggle,
  }: {
    item: FAQItem;
    isOpen: boolean;
    onToggle: () => void;
  }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-3">
      <button
        onClick={onToggle}
        className="w-full px-4 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
      >
        <span className="text-sm font-medium text-gray-800 pr-4">
          {item.question}
        </span>
        <svg
          className={`w-5 h-5 text-gray-500 transform transition-transform ${
            isOpen ? "rotate-180" : ""
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </button>
      {isOpen && (
        <div className="px-4 pb-4">
          <p className="text-sm text-gray-600 leading-relaxed">{item.answer}</p>
        </div>
      )}
    </div>
  );

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2
            className="text-3xl md:text-4xl font-bold"
            style={{ color: "#0D3166" }}
          >
            Frequently Asked Questions
          </h2>
        </div>

        {/* FAQ Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {isLoading ? (
            // Loading skeleton
            <>
              <div>
                {[...Array(4)].map((_, index) => (
                  <div
                    key={index}
                    className="bg-white rounded-lg border border-gray-200 mb-4"
                  >
                    <div className="px-4 py-4">
                      <Skeleton className="h-4 w-3/4 mb-2" />
                      <Skeleton className="h-3 w-1/2" />
                    </div>
                  </div>
                ))}
              </div>
              <div>
                {[...Array(4)].map((_, index) => (
                  <div
                    key={index}
                    className="bg-white rounded-lg border border-gray-200 mb-4"
                  >
                    <div className="px-4 py-4">
                      <Skeleton className="h-4 w-3/4 mb-2" />
                      <Skeleton className="h-3 w-1/2" />
                    </div>
                  </div>
                ))}
              </div>
            </>
          ) : error ? (
            <div className="col-span-2 text-center py-8">
              <div className="text-red-500 mb-4">Failed to load FAQs</div>
              <button
                onClick={() => window.location.reload()}
                className="bg-[#0D3166] text-white px-4 py-2 rounded-md hover:bg-[#1a4480]"
              >
                Retry
              </button>
            </div>
          ) : faqData.length === 0 ? (
            <div className="col-span-2 text-center py-8 text-gray-500">
              No FAQs available
            </div>
          ) : (
            <>
              {/* Left Column */}
              <div>
                {leftColumnItems.map((item: any, index: number) => (
                  <FAQItem
                    key={item.id || index}
                    item={item}
                    isOpen={openItems.includes(index)}
                    onToggle={() => toggleItem(index)}
                  />
                ))}
              </div>

              {/* Right Column */}
              <div>
                {rightColumnItems.map((item: any, index: number) => {
                  const actualIndex = index + leftColumnItems.length;
                  return (
                    <FAQItem
                      key={item.id || actualIndex}
                      item={item}
                      isOpen={openItems.includes(actualIndex)}
                      onToggle={() => toggleItem(actualIndex)}
                    />
                  );
                })}
              </div>
            </>
          )}
        </div>
      </div>
    </section>
  );
};

export default FAQSection;
