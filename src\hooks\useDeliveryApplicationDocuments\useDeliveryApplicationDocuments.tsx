import { useSDK } from "@/hooks/useSDK";
import { useState, useEffect } from "react";
import { useContexts } from "../useContexts";
import { ToastStatusEnum } from "@/utils/Enums";

interface Document {
  id: number;
  document_type: string;
  document_url: string;
  status: string;
  admin_remark: string;
}

export const useDeliveryApplicationDocuments = (applicationId: number) => {
  const { sdk } = useSDK();
  const { globalDispatch } = useContexts();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      const res = await (
        sdk as any
      ).ebadollar.admin.deliveryApplications.getDocuments({
        application_id: applicationId,
      });
      setDocuments(res.data);
    } catch (error: any) {
      globalDispatch({
        type: "SNACKBAR",
        payload: { message: error.message, toastStatus: ToastStatusEnum.ERROR },
      });
    } finally {
      setLoading(false);
    }
  };

  const updateDocument = async (
    documentId: number,
    status: "approved" | "rejected",
    admin_remark: string
  ) => {
    try {
      await (sdk as any).ebadollar.admin.deliveryDocuments.update(
        { document_id: documentId },
        {
          status,
          admin_remark,
        }
      );
      globalDispatch({
        type: "SNACKBAR",
        payload: {
          message: "Document updated successfully",
          toastStatus: ToastStatusEnum.SUCCESS,
        },
      });
      fetchDocuments(); // Refresh the list
    } catch (error: any) {
      globalDispatch({
        type: "SNACKBAR",
        payload: { message: error.message, toastStatus: ToastStatusEnum.ERROR },
      });
    }
  };

  useEffect(() => {
    if (applicationId) {
      fetchDocuments();
    }
  }, [applicationId]);

  return { documents, loading, updateDocument, fetchDocuments };
};
