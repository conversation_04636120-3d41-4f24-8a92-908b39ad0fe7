import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useSDK } from "../hooks/useSDK";
import { useToast } from "../components/Toast";
import { ToastStatusEnum } from "../utils/Enums";

// Interfaces
export interface ITransactionFilters
  extends Record<string, string | number | boolean | undefined> {
  page?: number;
  limit?: number;
  search?: string;
  dateRange?: string;
  transactionType?: string;
  status?: string;
  tab?: string;
}

export interface ITransaction {
  id: number;
  date: string;
  listing: string;
  type: string;
  counterparty: string;
  amount: string;
  fee: string;
  net_received_paid: string;
  status: string;
  listingId?: number;
  description?: string;
  createdAt: string;
  updatedAt: string;
  transactionGroupId?: string;
  referenceId?: string;
  paymentMethod?: string;
}

export interface ITransactionSummary {
  sales: {
    total: string;
    count: number;
    currency: string;
  };
  purchases: {
    total: string;
    count: number;
    currency: string;
  };
  fees: {
    total: string;
    average: string;
    currency: string;
  };
}

// Transactions Query
export const useTransactionsQuery = (filters: ITransactionFilters = {}) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["transactions", filters],
    queryFn: async () => {
      try {
        // Filter out undefined values for SDK compatibility
        const cleanFilters = Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== undefined)
        ) as Record<string, string | number>;

        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/transactions",
          method: "GET",
          params: cleanFilters,
        });
        return response;
      } catch (error) {
        throw error;
      }
    },
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Transaction Summary Query
export const useTransactionSummaryQuery = () => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["transaction-summary"],
    queryFn: async () => {
      try {
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/transactions/summary",
          method: "GET",
        });
        return response;
      } catch (error) {
        throw error;
      }
    },
    staleTime: 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Single Transaction Query
export const useTransactionQuery = (transactionId: number) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["transaction", transactionId],
    queryFn: async () => {
      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/transactions/${transactionId}`,
          method: "GET",
        });
        return response;
      } catch (error) {
        throw error;
      }
    },
    enabled: !!transactionId && transactionId > 0,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Export Transaction Data Mutation (placeholder for future implementation)
export const useExportTransactionsMutation = () => {
  const { sdk } = useSDK();
  const _queryClient = useQueryClient();
  const { success, error: _error } = useToast();

  return useMutation({
    mutationFn: async ({
      format,
      filters,
    }: {
      format: "csv" | "excel" | "pdf";
      filters?: ITransactionFilters;
    }) => {
      try {
        // This would be implemented when export functionality is needed
        const response = await sdk.request({
          endpoint: "/v2/api/ebadollar/custom/member/transactions/export",
          method: "POST",
          body: { format, filters },
        });
        return response;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (_data, variables) => {
      success(`Transactions exported as ${variables.format.toUpperCase()}`);
    },
    onError: (error: any) => {
      const errorMessage =
        error?.response?.data?.message || "Failed to export transactions";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};

// Dispute Transaction Mutation (placeholder for future implementation)
export const useDisputeTransactionMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error: _error } = useToast();

  return useMutation({
    mutationFn: async ({
      transactionId,
      reason,
      description,
    }: {
      transactionId: number;
      reason: string;
      description: string;
    }) => {
      try {
        // This would be implemented when dispute functionality is needed
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/transactions/${transactionId}/dispute`,
          method: "POST",
          body: { reason, description },
        });
        return response;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (_data, variables) => {
      success("Transaction dispute submitted successfully");

      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
      queryClient.invalidateQueries({
        queryKey: ["transaction", variables.transactionId],
      });
      queryClient.invalidateQueries({ queryKey: ["transaction-summary"] });
    },
    onError: (error: any) => {
      const errorMessage =
        error?.response?.data?.message || "Failed to submit dispute";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};

// Request Refund Mutation (placeholder for future implementation)
export const useRequestRefundMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();
  const { success, error: _error } = useToast();

  return useMutation({
    mutationFn: async ({
      transactionId,
      reason,
      amount,
    }: {
      transactionId: number;
      reason: string;
      amount?: number;
    }) => {
      try {
        // This would be implemented when refund functionality is needed
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/transactions/${transactionId}/refund`,
          method: "POST",
          body: { reason, amount },
        });
        return response;
      } catch (error) {
        throw error;
      }
    },
    onSuccess: (_data, variables) => {
      success("Refund request submitted successfully");

      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
      queryClient.invalidateQueries({
        queryKey: ["transaction", variables.transactionId],
      });
      queryClient.invalidateQueries({ queryKey: ["transaction-summary"] });
    },
    onError: (error: any) => {
      const errorMessage =
        error?.response?.data?.message || "Failed to request refund";
      error(errorMessage, ToastStatusEnum.ERROR);
    },
  });
};

// Helper function to format transaction status for display
export const formatTransactionStatus = (
  status: string
): { text: string; color: string } => {
  switch (status.toLowerCase()) {
    case "completed":
      return { text: "Completed", color: "text-green-600 bg-green-100" };
    case "in dispute":
    case "disputed":
      return { text: "In Dispute", color: "text-red-600 bg-red-100" };
    case "refunded":
      return { text: "Refunded", color: "text-blue-600 bg-blue-100" };
    case "pending":
      return { text: "Pending", color: "text-yellow-600 bg-yellow-100" };
    default:
      return { text: status, color: "text-gray-600 bg-gray-100" };
  }
};

// Helper function to format transaction type for display
export const formatTransactionType = (
  type: string
): { text: string; color: string } => {
  switch (type.toLowerCase()) {
    case "sale":
      return { text: "Sale", color: "text-green-600" };
    case "purchase":
      return { text: "Purchase", color: "text-blue-600" };
    case "transaction":
      return { text: "Transaction", color: "text-gray-600" };
    default:
      return { text: type, color: "text-gray-600" };
  }
};
