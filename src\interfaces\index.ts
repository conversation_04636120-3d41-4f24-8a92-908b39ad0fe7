export type {
  Action,
  Column,
  ModalState,
  FilterState,
  ExternalData,
  ActionBindType,
  PaginationState,
  ColumnDataState,
} from "./table.interface";

export interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  role: string;
  is_delivery_agent: boolean;
  status: string;
  rating: number | null;
  joined: string;
  documents_status: string;
  verified: string;
  photo?: string;
}

export interface Document {
  id: number;
  user_id: number;
  document_type: string;
  document_url: string;
  status: string;
  admin_remark: string | null;
  created_at: string;
  updated_at: string;
}
