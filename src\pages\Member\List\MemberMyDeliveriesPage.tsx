import React, { useState } from "react";
import { MemberWrapper } from "../../../components/MemberWrapper";
import { Skeleton } from "../../../components/Skeleton";
import { PaginationBar } from "../../../components/PaginationBar";
import { DeliveryAgentGuard } from "../../../components/DeliveryAgentGuard";
import {
  useDeliveryAssignmentsQuery,
  useDeliveryAssignmentQuery,
  useUpdateDeliveryStatusMutation,
  useUpdateConfirmationCodeMutation,
  IDeliveryAssignment,
  IDeliveryAssignmentFilters,
} from "../../../query/useDeliveryAgent";

const MemberMyDeliveriesPage = () => {
  const [activeTab, setActiveTab] = useState<
    "Active Deliveries" | "Completed Deliveries"
  >("Active Deliveries");
  const [selectedDeliveryId, setSelectedDeliveryId] = useState<number | null>(
    null
  );
  const [confirmationCodeInput, setConfirmationCodeInput] = useState("");
  const [filters, setFilters] = useState<IDeliveryAssignmentFilters>({
    page: 1,
    limit: 10,
    status: "active",
    search: "",
  });

  // Query hooks
  const {
    data: deliveriesData,
    isLoading: deliveriesLoading,
    error: deliveriesError,
    refetch: refetchDeliveries,
  } = useDeliveryAssignmentsQuery(filters);

  const { data: selectedDeliveryData, isLoading: selectedDeliveryLoading } =
    useDeliveryAssignmentQuery(selectedDeliveryId || 0);

  const { mutate: updateStatus, isPending: isUpdatingStatus } =
    useUpdateDeliveryStatusMutation();
  const { mutate: updateConfirmationCode, isPending: isUpdatingConfirmation } =
    useUpdateConfirmationCodeMutation();

  // Get data from queries
  const deliveries = deliveriesData?.data || [];
  const pagination = (deliveriesData as any)?.data?.pagination;
  const selectedDelivery = selectedDeliveryData?.data;

  // Event handlers
  const handleTabChange = (
    tab: "Active Deliveries" | "Completed Deliveries"
  ) => {
    setActiveTab(tab);
    setFilters((prev) => ({
      ...prev,
      status: tab === "Active Deliveries" ? "active" : "completed",
      page: 1,
    }));
    setSelectedDeliveryId(null);
  };

  const handleDeliverySelect = (delivery: IDeliveryAssignment) => {
    setSelectedDeliveryId(delivery.id);
  };

  const handlePageChange = (page: number) => {
    setFilters((prev) => ({ ...prev, page }));
  };

  const handleConfirmationCodeUpdate = () => {
    if (selectedDeliveryId && confirmationCodeInput.trim()) {
      updateConfirmationCode({
        assignmentId: selectedDeliveryId,
        confirmationCode: confirmationCodeInput.trim(),
      });
      setConfirmationCodeInput("");
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "picked_up":
        return "bg-blue-100 text-blue-800";
      case "delivered":
        return "bg-green-100 text-green-800";
      case "assigned":
        return "bg-yellow-100 text-yellow-800";
      case "accepted":
        return "bg-purple-100 text-purple-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "picked_up":
        return "🚚";
      case "delivered":
        return "✅";
      case "assigned":
        return "📋";
      case "accepted":
        return "👍";
      case "cancelled":
        return "❌";
      default:
        return "📦";
    }
  };

  const getStatusDisplay = (status: string) => {
    switch (status.toLowerCase()) {
      case "picked_up":
        return "In Transit";
      case "delivered":
        return "Delivered";
      case "assigned":
        return "Assigned";
      case "accepted":
        return "Accepted";
      case "cancelled":
        return "Cancelled";
      default:
        return status;
    }
  };

  return (
    <DeliveryAgentGuard>
      <MemberWrapper>
        <div className="h-full bg-[#0F2C59] overflow-auto">
          <div className="p-6">
            {/* Header */}
            <div className="mb-6">
              <h1 className="text-3xl font-bold text-white mb-2">
                My Deliveries
              </h1>
            </div>

            {/* Tabs */}
            <div className="mb-6">
              <div className="flex border-b border-gray-600">
                {(["Active Deliveries", "Completed Deliveries"] as const).map(
                  (tab) => (
                    <button
                      key={tab}
                      onClick={() => handleTabChange(tab)}
                      className={`px-6 py-3 text-sm font-medium border-b-2 ${
                        activeTab === tab
                          ? "border-[#E63946] text-white"
                          : "border-transparent text-gray-400 hover:text-gray-300"
                      }`}
                    >
                      {tab}
                    </button>
                  )
                )}
              </div>
            </div>

            {/* Main Content */}
            <div className="space-y-6">
              {/* Deliveries List */}
              <div className="bg-white rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                          Delivery ID
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                          Pickup From
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                          Deliver To
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                          Fee
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                          Status
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {deliveriesLoading ? (
                        // Loading skeleton
                        [...Array(5)].map((_, index) => (
                          <tr key={index}>
                            <td className="px-4 py-3">
                              <Skeleton className="h-4 w-20" />
                            </td>
                            <td className="px-4 py-3">
                              <div className="space-y-1">
                                <Skeleton className="h-4 w-24" />
                                <Skeleton className="h-3 w-20" />
                              </div>
                            </td>
                            <td className="px-4 py-3">
                              <div className="space-y-1">
                                <Skeleton className="h-4 w-24" />
                                <Skeleton className="h-3 w-20" />
                              </div>
                            </td>
                            <td className="px-4 py-3">
                              <Skeleton className="h-4 w-16" />
                            </td>
                            <td className="px-4 py-3">
                              <Skeleton className="h-6 w-20 rounded-full" />
                            </td>
                            <td className="px-4 py-3">
                              <Skeleton className="h-6 w-6" />
                            </td>
                          </tr>
                        ))
                      ) : deliveriesError ? (
                        <tr>
                          <td colSpan={6} className="px-4 py-8 text-center">
                            <div className="text-gray-500 mb-4">
                              Failed to load deliveries
                            </div>
                            <button
                              onClick={() => refetchDeliveries()}
                              className="bg-[#E63946] text-white px-4 py-2 rounded-md hover:bg-red-700"
                            >
                              Retry
                            </button>
                          </td>
                        </tr>
                      ) : deliveries.length === 0 ? (
                        <tr>
                          <td
                            colSpan={6}
                            className="px-4 py-8 text-center text-gray-500"
                          >
                            No {activeTab.toLowerCase()} found
                          </td>
                        </tr>
                      ) : (
                        deliveries.map((delivery: any) => (
                          <tr
                            key={delivery.id}
                            className={`hover:bg-gray-50 cursor-pointer ${
                              selectedDeliveryId === delivery.id
                                ? "bg-blue-50"
                                : ""
                            }`}
                            onClick={() => handleDeliverySelect(delivery)}
                          >
                            <td className="px-4 py-3 text-sm font-medium text-gray-900">
                              {delivery.deliveryId}
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900">
                              <div>
                                <div className="font-medium">
                                  {delivery.pickupFrom}
                                </div>
                                <div className="text-gray-500 text-xs">
                                  {delivery.pickupLocation}
                                </div>
                              </div>
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900">
                              <div>
                                <div className="font-medium">
                                  {delivery.deliverTo}
                                </div>
                                <div className="text-gray-500 text-xs">
                                  {delivery.deliverLocation}
                                </div>
                              </div>
                            </td>
                            <td className="px-4 py-3 text-sm font-medium text-gray-900">
                              {delivery.fee}
                            </td>
                            <td className="px-4 py-3 text-sm">
                              <span
                                className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
                                  delivery.status
                                )}`}
                              >
                                {getStatusDisplay(delivery.status)}
                              </span>
                            </td>
                            <td className="px-4 py-3 text-sm">
                              <button className="text-gray-400 hover:text-gray-600">
                                👁
                              </button>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Pagination */}
              {pagination && pagination.totalPages > 1 && (
                <div className="flex justify-center">
                  <PaginationBar
                    currentPage={pagination.page}
                    pageCount={pagination.totalPages || 1}
                    pageSize={pagination.limit || 10}
                    canPreviousPage={pagination.page > 1}
                    canNextPage={pagination.page < (pagination.totalPages || 1)}
                    updatePageSize={() => {}}
                    updateCurrentPage={handlePageChange}
                    startSize={10}
                    multiplier={10}
                    canChangeLimit={false}
                  />
                </div>
              )}

              {/* Delivery Details */}
              {selectedDeliveryId && (
                <div className="bg-[#0F2C59] rounded-lg p-6">
                  {selectedDeliveryLoading ? (
                    <div className="space-y-6">
                      <div className="flex items-center justify-between mb-6">
                        <Skeleton className="h-6 w-64" />
                        <Skeleton className="h-6 w-24 rounded-full" />
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-6">
                          <div className="bg-white rounded-lg p-4">
                            <Skeleton className="h-5 w-32 mb-4" />
                            <div className="space-y-3">
                              <Skeleton className="h-16 w-full" />
                              <Skeleton className="h-16 w-full" />
                            </div>
                          </div>
                        </div>
                        <div className="space-y-6">
                          <div className="bg-white rounded-lg p-4">
                            <Skeleton className="h-5 w-32 mb-4" />
                            <div className="space-y-3">
                              <Skeleton className="h-16 w-full" />
                              <Skeleton className="h-16 w-full" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : selectedDelivery ? (
                    <>
                      <div className="flex items-center justify-between mb-6">
                        <h2 className="text-xl font-bold text-white">
                          Delivery Details: {selectedDelivery.deliveryId}
                        </h2>
                        <span
                          className={`px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(
                            selectedDelivery.status
                          )}`}
                        >
                          {getStatusIcon(selectedDelivery.status)}{" "}
                          {getStatusDisplay(selectedDelivery.status)}
                        </span>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Left Column */}
                        <div className="space-y-6">
                          {/* Package Information */}
                          <div className="bg-white rounded-lg p-4">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">
                              Package Information
                            </h3>
                            <div className="space-y-3">
                              <div className="flex items-start">
                                <span className="text-gray-600 mr-3 mt-1">
                                  📦
                                </span>
                                <div>
                                  <div className="font-medium text-gray-900">
                                    {selectedDelivery.packageType}
                                  </div>
                                  {selectedDelivery.packageWeight && (
                                    <div className="text-sm text-gray-500">
                                      Weight: {selectedDelivery.packageWeight}{" "}
                                      kg
                                    </div>
                                  )}
                                  {selectedDelivery.packageDimensions && (
                                    <div className="text-sm text-gray-500">
                                      Dimensions:{" "}
                                      {selectedDelivery.packageDimensions}
                                    </div>
                                  )}
                                </div>
                              </div>
                              {selectedDelivery.packageDescription && (
                                <div className="flex items-start">
                                  <span className="text-gray-600 mr-3 mt-1">
                                    📝
                                  </span>
                                  <div className="font-medium text-gray-900">
                                    {selectedDelivery.packageDescription}
                                  </div>
                                </div>
                              )}
                              {selectedDelivery.requiresFragileHandling && (
                                <div className="flex items-start">
                                  <span className="text-gray-600 mr-3 mt-1">
                                    ⚠️
                                  </span>
                                  <div className="font-medium text-orange-600">
                                    Fragile - Handle with care
                                  </div>
                                </div>
                              )}
                              {selectedDelivery.requiresSignature && (
                                <div className="flex items-start">
                                  <span className="text-gray-600 mr-3 mt-1">
                                    ✍️
                                  </span>
                                  <div className="font-medium text-blue-600">
                                    Signature required
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Pickup Information */}
                          <div className="bg-white rounded-lg p-4">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">
                              Pickup Information
                            </h3>
                            <div className="space-y-3">
                              <div className="flex items-start">
                                <span className="text-gray-600 mr-3 mt-1">
                                  🏪
                                </span>
                                <div>
                                  <div className="font-medium text-gray-900">
                                    {selectedDelivery.pickupFrom}
                                  </div>
                                  {selectedDelivery.pickupContactName && (
                                    <div className="text-sm text-gray-500">
                                      {selectedDelivery.pickupContactName}
                                    </div>
                                  )}
                                </div>
                              </div>
                              <div className="flex items-start">
                                <span className="text-gray-600 mr-3 mt-1">
                                  📍
                                </span>
                                <div>
                                  <div className="font-medium text-gray-900">
                                    {selectedDelivery.pickupAddress}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    {selectedDelivery.pickupCity},{" "}
                                    {selectedDelivery.pickupProvince}
                                    {selectedDelivery.pickupPostalCode &&
                                      ` ${selectedDelivery.pickupPostalCode}`}
                                  </div>
                                </div>
                              </div>
                              {selectedDelivery.pickupContactPhone && (
                                <div className="flex items-start">
                                  <span className="text-gray-600 mr-3 mt-1">
                                    📞
                                  </span>
                                  <div className="font-medium text-gray-900">
                                    {selectedDelivery.pickupContactPhone}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Right Column */}
                        <div className="space-y-6">
                          {/* Delivery Information */}
                          <div className="bg-white rounded-lg p-4">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">
                              Delivery Information
                            </h3>
                            <div className="space-y-3">
                              <div className="flex items-start">
                                <span className="text-gray-600 mr-3 mt-1">
                                  👤
                                </span>
                                <div>
                                  <div className="font-medium text-gray-900">
                                    {selectedDelivery.deliverTo}
                                  </div>
                                  {selectedDelivery.dropoffContactName && (
                                    <div className="text-sm text-gray-500">
                                      {selectedDelivery.dropoffContactName}
                                    </div>
                                  )}
                                </div>
                              </div>
                              <div className="flex items-start">
                                <span className="text-gray-600 mr-3 mt-1">
                                  📍
                                </span>
                                <div>
                                  <div className="font-medium text-gray-900">
                                    {selectedDelivery.dropoffAddress}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    {selectedDelivery.dropoffCity},{" "}
                                    {selectedDelivery.dropoffProvince}
                                    {selectedDelivery.dropoffPostalCode &&
                                      ` ${selectedDelivery.dropoffPostalCode}`}
                                  </div>
                                </div>
                              </div>
                              {selectedDelivery.dropoffContactPhone && (
                                <div className="flex items-start">
                                  <span className="text-gray-600 mr-3 mt-1">
                                    📞
                                  </span>
                                  <div className="font-medium text-gray-900">
                                    {selectedDelivery.dropoffContactPhone}
                                  </div>
                                </div>
                              )}
                              {selectedDelivery.deliveryDeadline && (
                                <div className="flex items-start">
                                  <span className="text-gray-600 mr-3 mt-1">
                                    🕐
                                  </span>
                                  <div className="font-medium text-gray-900">
                                    Deliver by{" "}
                                    {new Date(
                                      selectedDelivery.deliveryDeadline
                                    ).toLocaleString()}
                                  </div>
                                </div>
                              )}
                              {selectedDelivery.specialInstructions && (
                                <div className="flex items-start">
                                  <span className="text-gray-600 mr-3 mt-1">
                                    📋
                                  </span>
                                  <div>
                                    <div className="font-medium text-gray-900 mb-1">
                                      Special Instructions:
                                    </div>
                                    <div className="text-sm text-gray-600">
                                      {selectedDelivery.specialInstructions}
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Delivery Status & Actions */}
                          <div className="bg-white rounded-lg p-4">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">
                              Delivery Status & Actions
                            </h3>
                            <div className="space-y-4">
                              <div className="flex items-center justify-between">
                                <span className="text-gray-600">
                                  Current Status:
                                </span>
                                <span
                                  className={`px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(
                                    selectedDelivery.status
                                  )}`}
                                >
                                  {getStatusDisplay(selectedDelivery.status)}
                                </span>
                              </div>

                              <div className="flex items-center justify-between">
                                <span className="text-gray-600">
                                  Delivery Fee:
                                </span>
                                <span className="font-medium text-gray-900">
                                  {selectedDelivery.fee}
                                </span>
                              </div>

                              {selectedDelivery.estimatedDistance && (
                                <div className="flex items-center justify-between">
                                  <span className="text-gray-600">
                                    Distance:
                                  </span>
                                  <span className="font-medium text-gray-900">
                                    {selectedDelivery.estimatedDistance} km
                                  </span>
                                </div>
                              )}

                              {selectedDelivery.confirmationCode && (
                                <div className="flex items-center justify-between">
                                  <span className="text-gray-600">
                                    Confirmation Code:
                                  </span>
                                  <span className="font-mono font-medium text-gray-900">
                                    {selectedDelivery.confirmationCode}
                                  </span>
                                </div>
                              )}

                              {/* Action Buttons */}
                              <div className="flex gap-2">
                                {selectedDelivery.status === "assigned" && (
                                  <button
                                    onClick={() =>
                                      updateStatus({
                                        assignmentId: selectedDelivery.id,
                                        status: "accepted",
                                      })
                                    }
                                    disabled={isUpdatingStatus}
                                    className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                                  >
                                    {isUpdatingStatus
                                      ? "Processing..."
                                      : "Accept Delivery"}
                                  </button>
                                )}

                                {selectedDelivery.status === "accepted" && (
                                  <button
                                    onClick={() =>
                                      updateStatus({
                                        assignmentId: selectedDelivery.id,
                                        status: "picked_up",
                                      })
                                    }
                                    disabled={isUpdatingStatus}
                                    className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                                  >
                                    {isUpdatingStatus
                                      ? "Processing..."
                                      : "Mark as Picked Up"}
                                  </button>
                                )}

                                {selectedDelivery.status === "picked_up" && (
                                  <button
                                    onClick={() =>
                                      updateStatus({
                                        assignmentId: selectedDelivery.id,
                                        status: "delivered",
                                      })
                                    }
                                    disabled={isUpdatingStatus}
                                    className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                                  >
                                    {isUpdatingStatus
                                      ? "Processing..."
                                      : "Mark as Delivered"}
                                  </button>
                                )}

                                {["assigned", "accepted", "picked_up"].includes(
                                  selectedDelivery.status
                                ) && (
                                  <button
                                    onClick={() =>
                                      updateStatus({
                                        assignmentId: selectedDelivery.id,
                                        status: "cancelled",
                                      })
                                    }
                                    disabled={isUpdatingStatus}
                                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                                  >
                                    {isUpdatingStatus
                                      ? "Processing..."
                                      : "Cancel"}
                                  </button>
                                )}
                              </div>

                              {/* Agent Notes */}
                              {selectedDelivery.agentNotes && (
                                <div className="pt-4 border-t">
                                  <h4 className="text-sm font-medium text-gray-700 mb-2">
                                    Agent Notes:
                                  </h4>
                                  <p className="text-sm text-gray-600">
                                    {selectedDelivery.agentNotes}
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Delivery Log */}
                      {selectedDelivery.logs &&
                        selectedDelivery.logs.length > 0 && (
                          <div className="bg-white rounded-lg p-4">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">
                              Delivery Log
                            </h3>
                            <div className="space-y-3">
                              {selectedDelivery.logs.map((log: any) => (
                                <div
                                  key={log.id}
                                  className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg"
                                >
                                  <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                                  <div className="flex-1">
                                    <div className="flex items-center justify-between">
                                      <span className="text-sm font-medium text-gray-900">
                                        {log.message}
                                      </span>
                                      <span className="text-xs text-gray-500">
                                        {new Date(
                                          log.createdAt
                                        ).toLocaleString()}
                                      </span>
                                    </div>
                                    {log.loggedBy && (
                                      <div className="text-xs text-gray-500 mt-1">
                                        by {log.loggedBy}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                    </>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-300">
                        Failed to load delivery details
                      </p>
                    </div>
                  )}
                  {/* Confirmation Code Section */}
                  {selectedDelivery &&
                    ["accepted", "picked_up"].includes(
                      selectedDelivery.status
                    ) && (
                      <div className="mt-6">
                        <div className="bg-white rounded-lg p-4">
                          <h3 className="text-lg font-semibold text-gray-900 mb-4">
                            Update Confirmation Code
                          </h3>
                          <div className="flex gap-3">
                            <input
                              type="text"
                              placeholder="Enter code"
                              value={confirmationCodeInput}
                              onChange={(e) =>
                                setConfirmationCodeInput(e.target.value)
                              }
                              className="flex-1 px-4 py-3 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent"
                            />
                            <button
                              onClick={handleConfirmationCodeUpdate}
                              disabled={
                                !confirmationCodeInput.trim() ||
                                isUpdatingConfirmation
                              }
                              className="px-6 py-3 bg-[#0F2C59] text-white rounded-md text-sm font-medium hover:bg-[#1a3a6b] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isUpdatingConfirmation
                                ? "Updating..."
                                : "Update"}
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                </div>
              )}
            </div>
          </div>
        </div>
      </MemberWrapper>
    </DeliveryAgentGuard>
  );
};

export default MemberMyDeliveriesPage;
