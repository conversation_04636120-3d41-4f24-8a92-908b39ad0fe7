import React from "react";

interface SettingIconProps {
  className?: string;
  onClick?: () => void;
}

const SettingIcon = ({ className = "", onClick }: SettingIconProps) => {
  return (
    <svg
      onClick={onClick}
      className={`${className}`}
      width="19"
      height="15"
      viewBox="0 0 19 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_6_703)">
        <g clip-path="url(#clip1_6_703)">
          <path
            d="M7.04687 0.75C7.97513 0.75 8.86537 1.11875 9.52175 1.77513C10.1781 2.4315 10.5469 3.32174 10.5469 4.25C10.5469 5.17826 10.1781 6.0685 9.52175 6.72487C8.86537 7.38125 7.97513 7.75 7.04687 7.75C6.11862 7.75 5.22838 7.38125 4.572 6.72487C3.91562 6.0685 3.54687 5.17826 3.54687 4.25C3.54687 3.32174 3.91562 2.4315 4.572 1.77513C5.22838 1.11875 6.11862 0.75 7.04687 0.75ZM5.79727 9.0625H8.29648C8.61914 9.0625 8.93633 9.09531 9.23984 9.15273C9.18242 9.65859 9.44219 10.1262 9.83594 10.3777C9.38203 10.6676 9.10586 11.2418 9.28906 11.8352C9.39844 12.1879 9.54609 12.5324 9.7375 12.8633C9.92891 13.1941 10.1531 13.4949 10.4047 13.7656C10.834 14.2277 11.4875 14.2688 11.9687 14.0035V14.0281C11.9687 14.2797 12.0426 14.534 12.1848 14.7473H1.73398C1.28555 14.75 0.921875 14.3863 0.921875 13.9379C0.921875 11.2445 3.10391 9.0625 5.79727 9.0625ZM12.8437 6.71641C12.8437 6.525 12.9668 6.35273 13.1527 6.31172C13.4398 6.24609 13.7406 6.21055 14.0469 6.21055C14.3531 6.21055 14.6539 6.24609 14.941 6.31172C15.127 6.35273 15.25 6.525 15.25 6.71641V7.55312C15.466 7.64609 15.6711 7.76367 15.8598 7.90313L16.5406 7.51211C16.7074 7.41641 16.9152 7.43828 17.0465 7.57773C17.2543 7.79922 17.4375 8.04805 17.5961 8.32148C17.7547 8.59492 17.8777 8.8793 17.9652 9.16914C18.0227 9.35234 17.9352 9.54375 17.7684 9.63945L17.0848 10.0332C17.0957 10.1426 17.1039 10.2547 17.1039 10.3695C17.1039 10.4844 17.0984 10.5938 17.0848 10.7059L17.7684 11.0996C17.9352 11.1953 18.0199 11.3867 17.9652 11.5699C17.875 11.8598 17.752 12.1441 17.5961 12.4176C17.4402 12.691 17.2543 12.9398 17.0465 13.1613C16.9152 13.3008 16.7047 13.3227 16.5406 13.227L15.8598 12.8359C15.6711 12.9754 15.4687 13.093 15.25 13.1859V14.0227C15.25 14.2141 15.127 14.3863 14.941 14.4273C14.6539 14.493 14.3531 14.5285 14.0469 14.5285C13.7406 14.5285 13.4398 14.493 13.1527 14.4273C12.9668 14.3863 12.8437 14.2141 12.8437 14.0227V13.1859C12.625 13.093 12.4172 12.9754 12.2285 12.8332L11.5531 13.2242C11.3863 13.3199 11.1785 13.298 11.0473 13.1586C10.8395 12.9371 10.6562 12.6883 10.4977 12.4148C10.3391 12.1414 10.216 11.857 10.1285 11.5672C10.0711 11.384 10.1586 11.1926 10.3254 11.0969L11.0035 10.7059C10.9926 10.5938 10.9844 10.4816 10.9844 10.3668C10.9844 10.252 10.9898 10.1398 11.0035 10.0277L10.3227 9.63672C10.1559 9.54102 10.0711 9.34961 10.1258 9.16641C10.216 8.87656 10.3363 8.59219 10.4949 8.31875C10.6535 8.04531 10.8367 7.79648 11.0445 7.575C11.1758 7.43555 11.3836 7.41367 11.5504 7.50938L12.2285 7.90039C12.4172 7.76094 12.625 7.64336 12.8437 7.54766V6.71641ZM15.3621 10.3668C15.3702 10.1892 15.3422 10.0118 15.2799 9.84537C15.2175 9.6789 15.122 9.52681 14.9992 9.39828C14.8764 9.26974 14.7288 9.16744 14.5654 9.09754C14.4019 9.02765 14.226 8.99161 14.0482 8.99161C13.8705 8.99161 13.6946 9.02765 13.5311 9.09754C13.3677 9.16744 13.2201 9.26974 13.0973 9.39828C12.9745 9.52681 12.879 9.6789 12.8166 9.84537C12.7543 10.0118 12.7263 10.1892 12.7344 10.3668C12.7263 10.5444 12.7543 10.7218 12.8166 10.8882C12.879 11.0547 12.9745 11.2068 13.0973 11.3353C13.2201 11.4639 13.3677 11.5662 13.5311 11.6361C13.6946 11.7059 13.8705 11.742 14.0482 11.742C14.226 11.742 14.4019 11.7059 14.5654 11.6361C14.7288 11.5662 14.8764 11.4639 14.9992 11.3353C15.122 11.2068 15.2175 11.0547 15.2799 10.8882C15.3422 10.7218 15.3702 10.5444 15.3621 10.3668Z"
            fill="#16A34A"
          />
        </g>
      </g>
      <defs>
        <clipPath id="clip0_6_703">
          <rect
            width="17.5"
            height="14"
            fill="white"
            transform="translate(0.921875 0.75)"
          />
        </clipPath>
        <clipPath id="clip1_6_703">
          <path d="M0.921875 0.75H18.4219V14.75H0.921875V0.75Z" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default SettingIcon;
