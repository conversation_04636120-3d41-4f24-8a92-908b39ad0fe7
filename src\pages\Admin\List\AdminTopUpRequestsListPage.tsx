import React, { useEffect, useState } from "react";
import { AdminWrapper } from "../../../components/AdminWrapper";
import { MkdInputV2 } from "../../../components/MkdInputV2";
import InteractiveButton from "../../../components/InteractiveButton/InteractiveButton";
import { CheckIcon, EyeIcon, XIcon } from "../../../assets/svgs";
import { useSDK } from "../../../hooks/useSDK";
import { showToast, GlobalContext } from "../../../context/Global";
import { AuthContext } from "../../../context/Auth";
import { getNonNullValue } from "../../../utils/utils";

interface TopUpRequest {
  id: number;
  user: {
    email: string;
    data: {
      first_name?: string;
      last_name?: string;
    };
  };
  payment_method: string;
  amount: number;
  transaction_id: string;
  payment_date: string;
  proof_url: string;
  status: "pending" | "approved" | "rejected";
  admin_remark: string;
}

const columns = [
  {
    header: "",
    accessor: "checkbox",
  },
  { header: "Request ID", accessor: "id" },
  { header: "User Name", accessor: "user_name" },
  { header: "Email", accessor: "email" },
  { header: "Payment Method", accessor: "payment_method" },
  { header: "Amount (USD)", accessor: "amount" },
  { header: "Transaction ID", accessor: "transaction_id" },
  { header: "Payment Date", accessor: "payment_date" },
  {
    header: "Proof",
    accessor: "proof",
  },
  {
    header: "Status",
    accessor: "status",
  },
  { header: "Admin Remark", accessor: "admin_remark" },
];

const AdminTopUpRequestsListPage = () => {
  const [requests, setRequests] = useState<TopUpRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    search: "",
    status: "all",
    payment_method: "all",
  });
  const [selected, setSelected] = useState<number[]>([]);
  const { sdk } = useSDK();
  const { dispatch } = React.useContext(GlobalContext);
  const { state: authState } = React.useContext(AuthContext);

  const fetchData = async () => {
    try {
      setLoading(true);
      const params: any = {
        page: 1,
        limit: 10,
      };
      if (filters.search) {
        params["search"] = filters.search;
      }
      if (filters.status !== "all") {
        params["status"] = filters.status;
      }
      if (filters.payment_method !== "all") {
        params["payment_method"] = filters.payment_method;
      }

      const result = await sdk.callRawAPI(
        "/v1/api/ebadollar/custom/admin/topup-requests",
        params,
        "GET"
      );

      if (!result.error) {
        setRequests(result.list);
      } else {
        showToast(dispatch, result.message);
      }
    } catch (error: any) {
      console.log("ERROR", error);
      showToast(dispatch, error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFilters((prev) => ({ ...prev, [name]: value }));
  };

  const handleApplyFilters = () => {
    fetchData();
  };

  const handleSelection = (id: number) => {
    if (selected.includes(id)) {
      setSelected(selected.filter((item) => item !== id));
    } else {
      setSelected([...selected, id]);
    }
  };

  const handleSelectAll = () => {
    if (selected.length === requests.length) {
      setSelected([]);
    } else {
      setSelected(requests.map((req) => req.id));
    }
  };

  const handleApprove = async () => {
    try {
      const result = await sdk.callRawAPI(
        "/v1/api/ebadollar/custom/admin/topup-requests/approve",
        { ids: selected },
        "POST"
      );
      if (!result.error) {
        showToast(dispatch, "Requests approved!");
        fetchData();
        setSelected([]);
      } else {
        showToast(dispatch, result.message);
      }
    } catch (error: any) {
      console.log("ERROR", error);
      showToast(dispatch, error.message);
    }
  };

  const handleReject = async () => {
    try {
      // You might want a modal to get admin_remark
      const result = await sdk.callRawAPI(
        "/v1/api/ebadollar/custom/admin/topup-requests/reject",
        { ids: selected, admin_remark: "Rejected by admin" },
        "POST"
      );
      if (!result.error) {
        showToast(dispatch, "Requests rejected!");
        fetchData();
        setSelected([]);
      } else {
        showToast(dispatch, result.message);
      }
    } catch (error: any) {
      console.log("ERROR", error);
      showToast(dispatch, error.message);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <AdminWrapper>
      <div className="bg-white min-h-screen">
        {/* Header Section */}
        <div className="px-6 py-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-[#1E293B]">
                Top up requests Management
              </h1>
              <p className="text-sm text-gray-600 mt-1">
                Moderate and manage marketplace listings
              </p>
            </div>
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gray-300 rounded-full"></div>
            </div>
          </div>
        </div>

        {/* Filter Section */}
        <div className="px-6 py-6 bg-gray-50 border-b border-gray-200">
          <div className="flex items-end gap-4 ">
            <div className="flex-[2] ">
              <label className="  text-sm font-medium text-gray-700 mb-2 block">
                Search
              </label>
              <MkdInputV2
                name="search"
                value={filters.search}
                onChange={handleFilterChange}
                placeholder="Search email or transaction ID"
                className="!w-full "
              >
                <input className="!h-10 !rounded-md !border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59] !w-full" />
              </MkdInputV2>
            </div>
            <div className="w-40">
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Status
              </label>
              <MkdInputV2
                type="select"
                name="status"
                value={filters.status}
                onChange={handleFilterChange}
                className="w-full"
              >
                <select className="!h-10 !rounded-md !border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59] w-full">
                  <option value="all">All</option>
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                </select>
              </MkdInputV2>
            </div>
            <div className="w-40">
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Payment Method
              </label>
              <MkdInputV2
                type="select"
                name="payment_method"
                value={filters.payment_method}
                onChange={handleFilterChange}
                className="w-full"
              >
                <select className="!h-10 !rounded-md !border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59] w-full">
                  <option value="all">All</option>
                  <option value="paypal">PayPal</option>
                  <option value="bank_transfer">Bank Transfer</option>
                  <option value="credit_debit">Credit/Debit</option>
                  <option value="etransfer">e-Transfer</option>
                </select>
              </MkdInputV2>
            </div>
            <div>
              <InteractiveButton
                onClick={handleApplyFilters}
                className="!bg-[#1E293B] hover:!bg-gray-900 text-white px-6 py-2 h-10 rounded-md text-sm font-medium"
              >
                Apply filters
              </InteractiveButton>
            </div>
          </div>
        </div>

        {/* Table Section */}
        <div className="px-6 py-4">
          <div className="bg-white">
            <div className="flex justify-between items-center px-4 py-3 border-b border-gray-200">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  className="h-4 w-4 !border-[#D1D5DB] !focus:ring-[#0F2C59]"
                  onChange={handleSelectAll}
                  checked={
                    requests.length > 0 && selected.length === requests.length
                  }
                />
                <label className="ml-2 text-sm text-gray-700">Select All</label>
              </div>
              <div className="flex space-x-2">
                <InteractiveButton
                  onClick={handleApprove}
                  disabled={selected.length === 0}
                  className="!bg-green-600 hover:!bg-green-700 text-white flex items-center px-4 py-2 rounded text-sm disabled:opacity-50"
                >
                  <CheckIcon className="h-4 w-4 mr-2" />
                  Approve Selected
                </InteractiveButton>
                <InteractiveButton
                  onClick={handleReject}
                  disabled={selected.length === 0}
                  className="!bg-red-600 hover:!bg-red-700 text-white flex items-center px-4 py-2 rounded text-sm disabled:opacity-50"
                >
                  <XIcon className="h-4 w-4 mr-2" />
                  Reject Selected
                </InteractiveButton>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className="bg-gray-50">
                  <tr>
                    {columns.map((column, index) => (
                      <th
                        key={column.accessor}
                        scope="col"
                        className={`px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                          index === 0 ? "w-12" : ""
                        }`}
                      >
                        {column.header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-white">
                  {loading ? (
                    <tr>
                      <td
                        colSpan={11}
                        className="px-4 py-8 text-center text-gray-500"
                      >
                        Loading...
                      </td>
                    </tr>
                  ) : requests.length === 0 ? (
                    <tr>
                      <td
                        colSpan={11}
                        className="px-4 py-8 text-center text-gray-500"
                      >
                        No top-up requests found
                      </td>
                    </tr>
                  ) : (
                    requests.map((row: TopUpRequest, rowIndex) => (
                      <tr
                        key={rowIndex}
                        className="border-b border-gray-100 hover:bg-gray-50"
                      >
                        <td className="px-4 py-3">
                          <input
                            type="checkbox"
                            className="h-4 w-4 !border-[#D1D5DB] !focus:ring-[#0F2C59]"
                            checked={selected.includes(row.id)}
                            onChange={() => handleSelection(row.id)}
                          />
                        </td>
                        <td className="px-4 py-3 text-sm font-medium text-gray-900">
                          REQ-{row.id}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {getNonNullValue(row.user?.data?.first_name)}{" "}
                          {getNonNullValue(row.user?.data?.last_name)}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {row.user.email}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {row.payment_method === "paypal"
                            ? "PayPal"
                            : row.payment_method === "bank_transfer"
                              ? "Bank Transfer"
                              : row.payment_method === "credit_debit"
                                ? "Credit/Debit"
                                : row.payment_method === "etransfer"
                                  ? "e-Transfer"
                                  : row.payment_method}
                        </td>
                        <td className="px-4 py-3 text-sm font-semibold text-green-600">
                          ${Number(row.amount).toFixed(2)}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {row.transaction_id}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {new Date(row.payment_date).toLocaleDateString(
                            "en-US",
                            {
                              month: "short",
                              day: "numeric",
                              year: "numeric",
                            }
                          )}
                        </td>
                        <td className="px-4 py-3">
                          <a
                            href={row.proof_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:text-blue-800"
                          >
                            <EyeIcon className="h-5 w-5" />
                          </a>
                        </td>
                        <td className="px-4 py-3">
                          <span
                            className={`px-2 py-1 text-xs font-medium rounded-full ${
                              row.status === "approved"
                                ? "bg-green-100 text-green-800"
                                : row.status === "pending"
                                  ? "bg-yellow-100 text-yellow-800"
                                  : "bg-red-100 text-red-800"
                            }`}
                          >
                            {row.status.charAt(0).toUpperCase() +
                              row.status.slice(1)}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900">
                          {row.admin_remark || "-"}
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </AdminWrapper>
  );
};

export default AdminTopUpRequestsListPage;
