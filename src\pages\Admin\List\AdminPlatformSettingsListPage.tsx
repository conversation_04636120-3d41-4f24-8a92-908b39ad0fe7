import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { AdminWrapper } from "../../../components/AdminWrapper";
import MkdInputV2 from "../../../components/MkdInputV2";
import { InteractiveButton } from "../../../components/InteractiveButton";
import AlertCircle from "../../../assets/svgs/AlertCircle";
import {
  useGetPlatformSettings,
  useUpdatePlatformSettings,
} from "../../../query/usePlatformSettings";
import SnackBar from "../../../components/SnackBar/SnackBar";
import { MkdLoader } from "../../../components/MkdLoader";
import { useContexts } from "../../../hooks/useContexts";

const platformSettingsSchema = yup.object({
  active_listing_expiry: yup.number().required(),
  draft_listing_expiry: yup.number().required(),
  buy_from_community_fixed_fee: yup.number().required(),
  buy_from_community_percentage_fee: yup.number().required(),
  maximum_eba_credit_line_limit: yup.number().required(),
  refund_window: yup.number().required(),
  usd_fee_percentage_for_buyers: yup.number().required(),
  ebas_fee_percentage_for_sellers: yup.number().required(),
  ebas_purchase_price: yup.number().required(),
});

type PlatformSettingsForm = yup.InferType<typeof platformSettingsSchema>;

const defaultValues = {
  active_listing_expiry: 30,
  draft_listing_expiry: 30,
  buy_from_community_fixed_fee: 50,
  buy_from_community_percentage_fee: 5,
  maximum_eba_credit_line_limit: 5000,
  refund_window: 30,
  usd_fee_percentage_for_buyers: 5,
  ebas_fee_percentage_for_sellers: 5,
  ebas_purchase_price: 5,
};

// Test data for prefilling during development/testing
const testData = {
  active_listing_expiry: 45,
  draft_listing_expiry: 15,
  buy_from_community_fixed_fee: 75,
  buy_from_community_percentage_fee: 7.5,
  maximum_eba_credit_line_limit: 10000,
  refund_window: 14,
  usd_fee_percentage_for_buyers: 3.5,
  ebas_fee_percentage_for_sellers: 4.2,
  ebas_purchase_price: 8.99,
};

const AdminPlatformSettingsListPage = () => {
  const { data: settings, isLoading } = useGetPlatformSettings();
  const { globalDispatch } = useContexts();
  const { mutate: updateSettings, isPending: isUpdating } =
    useUpdatePlatformSettings();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    resolver: yupResolver(platformSettingsSchema),
    defaultValues: defaultValues,
  });

  useEffect(() => {
    if (settings?.data) {
      reset(settings.data);
    }
  }, [settings, reset]);

  const onSubmit = (data: PlatformSettingsForm) => {
    // Filter out timestamp and id fields that shouldn't be updated
    const { id, created_at, updated_at, ...updateData } = data as any;

    updateSettings(updateData, {
      onSuccess: (updatedData) => {
        globalDispatch({
          type: "SNACKBAR",
          payload: {
            message: "Settings updated successfully",
            toastStatus: "success",
          },
        });
        if (updatedData?.data) {
          reset(updatedData.data);
        }
      },
      onError: (error: any) => {
        globalDispatch({
          type: "SNACKBAR",
          payload: {
            message: error.message || "Failed to update settings",
            toastStatus: "error",
          },
        });
      },
    });
  };

  const handleReset = () => {
    reset(defaultValues);
  };

  const handlePrefillTestData = () => {
    reset(testData);
  };

  if (isLoading) {
    return (
      <AdminWrapper>
        <div className="flex justify-center items-center h-screen">
          <MkdLoader />
        </div>
      </AdminWrapper>
    );
  }

  return (
    <AdminWrapper>
      <SnackBar />
      <div className="bg-[#F8F9FB] p-6 min-h-screen">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-3xl font-bold text-[#1E293B]">
              Platform Settings
            </h1>
            <p className="text-[#667085]">
              Configure core marketplace parameters
            </p>
          </div>
          <img
            src="https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
            alt="Profile"
            className="h-10 w-10 rounded-full"
          />
        </div>
        <div className="bg-white p-8 rounded-lg shadow-md">
          <h2 className="text-lg font-semibold text-[#101828] mb-6">
            Core Settings
          </h2>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
              <MkdInputV2
                name="active_listing_expiry"
                type="number"
                register={register}
                errors={errors}
                required
              >
                <MkdInputV2.Label>
                  <div className="flex items-center">
                    Active Listing Expiry (days){" "}
                    <AlertCircle className="ml-2" />
                  </div>
                </MkdInputV2.Label>
                <MkdInputV2.Field />
                <MkdInputV2.Error />
              </MkdInputV2>

              <MkdInputV2
                name="draft_listing_expiry"
                type="number"
                register={register}
                errors={errors}
                required
              >
                <MkdInputV2.Label>
                  <div className="flex items-center">
                    Draft Listing Expiry (days) <AlertCircle className="ml-2" />
                  </div>
                </MkdInputV2.Label>
                <MkdInputV2.Field />
                <MkdInputV2.Error />
              </MkdInputV2>

              <MkdInputV2
                name="buy_from_community_fixed_fee"
                type="number"
                register={register}
                errors={errors}
                required
              >
                <MkdInputV2.Label>
                  <div className="flex items-center">
                    Buy from Community - Fixed Fee (USD){" "}
                    <AlertCircle className="ml-2" />
                  </div>
                </MkdInputV2.Label>
                <MkdInputV2.Field />
                <MkdInputV2.Error />
              </MkdInputV2>

              <MkdInputV2
                name="buy_from_community_percentage_fee"
                type="number"
                register={register}
                errors={errors}
                required
              >
                <MkdInputV2.Label>
                  <div className="flex items-center">
                    Buy from Community - Percentage Fee (%){" "}
                    <AlertCircle className="ml-2" />
                  </div>
                </MkdInputV2.Label>
                <MkdInputV2.Field />
                <MkdInputV2.Error />
              </MkdInputV2>

              <MkdInputV2
                name="maximum_eba_credit_line_limit"
                type="number"
                register={register}
                errors={errors}
                required
              >
                <MkdInputV2.Label>
                  <div className="flex items-center">
                    Maximum eBa Credit Line Limit{" "}
                    <AlertCircle className="ml-2" />
                  </div>
                </MkdInputV2.Label>
                <MkdInputV2.Field />
                <MkdInputV2.Error />
              </MkdInputV2>

              <MkdInputV2
                name="refund_window"
                type="number"
                register={register}
                errors={errors}
                required
              >
                <MkdInputV2.Label>
                  <div className="flex items-center">
                    Refund Window (days) <AlertCircle className="ml-2" />
                  </div>
                </MkdInputV2.Label>
                <MkdInputV2.Field />
                <MkdInputV2.Error />
              </MkdInputV2>

              <MkdInputV2
                name="usd_fee_percentage_for_buyers"
                type="number"
                register={register}
                errors={errors}
                required
              >
                <MkdInputV2.Label>
                  <div className="flex items-center">
                    USD Fee Percentage for Buyers{" "}
                    <AlertCircle className="ml-2" />
                  </div>
                </MkdInputV2.Label>
                <MkdInputV2.Field />
                <MkdInputV2.Error />
              </MkdInputV2>

              <MkdInputV2
                name="ebas_fee_percentage_for_sellers"
                type="number"
                register={register}
                errors={errors}
                required
              >
                <MkdInputV2.Label>
                  <div className="flex items-center">
                    eBa$ Fee Percentage for Sellers{" "}
                    <AlertCircle className="ml-2" />
                  </div>
                </MkdInputV2.Label>
                <MkdInputV2.Field />
                <MkdInputV2.Error />
              </MkdInputV2>

              <MkdInputV2
                name="ebas_purchase_price"
                type="number"
                register={register}
                errors={errors}
                required
              >
                <MkdInputV2.Label>
                  <div className="flex items-center">
                    eBa$ Purchase Price (USD) <AlertCircle className="ml-2" />
                  </div>
                </MkdInputV2.Label>
                <MkdInputV2.Field />
                <MkdInputV2.Error />
              </MkdInputV2>
            </div>
            <div className="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
              <div className="flex gap-3">
                <InteractiveButton
                  type="button"
                  className="!bg-white !text-gray-700 !border !border-gray-300 !hover:text-white !h-full !hover:bg-gray-50"
                  onClick={handleReset}
                >
                  Reset to Default
                </InteractiveButton>
                <InteractiveButton
                  type="button"
                  className="!bg-green-600 !text-white !border !border-green-600 !hover:bg-green-700"
                  onClick={handlePrefillTestData}
                >
                  Prefill Test Data
                </InteractiveButton>
              </div>
              <InteractiveButton
                type="submit"
                className="!bg-[#0F2C59] !text-white !hover:bg-blue-900"
                disabled={isUpdating}
              >
                {isUpdating ? "Saving..." : "Save Settings"}
              </InteractiveButton>
            </div>
          </form>
        </div>
      </div>
    </AdminWrapper>
  );
};

export default AdminPlatformSettingsListPage;
