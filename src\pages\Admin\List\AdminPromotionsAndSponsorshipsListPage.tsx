import React, { useState } from "react";
import { use<PERSON><PERSON>, SubmitHandler } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { AdminWrapper } from "../../../components/AdminWrapper";
import { MkdInputV2 } from "../../../components/MkdInputV2";
import { InteractiveButton } from "../../../components/InteractiveButton";
import MkdInputV2Label from "../../../components/MkdInputV2/MkdInputV2Label";
import MkdInputV2Field from "../../../components/MkdInputV2/MkdInputV2Field";
import { UserIcon } from "../../../assets/svgs";
import { MkdLoader } from "../../../components/MkdLoader";
import { ActionConfirmationModal } from "../../../components/ActionConfirmationModal";
import PaginationBar from "../../../components/PaginationBar/PaginationBar";
import { Skeleton } from "../../../components/Skeleton";
import {
  usePromotionSettingsQuery,
  useSponsoredListingsQuery,
  useUpdatePromotionSettingsMutation,
  useDeleteSponsoredListingMutation,
} from "../../../query/usePromotion";

interface IPromotionSettings {
  featured_listing_cost_eba: number;
  promotion_max_duration_days: number;
  maximum_active_promotions: number;
  featured_listing_cost_usd: number;
  allow_eba_currency?: boolean;
  allow_usd_currency?: boolean;
  allow_both_options?: boolean;
}

interface ISponsoredListing {
  id: number;
  listing_id: string;
  title: string;
  seller: string;
  start_date: string;
  end_date: string;
  status: string;
}

interface IPagination {
  page: number;
  limit: number;
  total: number;
  num_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

const promotionSettingsSchema = yup.object({
  featured_listing_cost_eba: yup.number().required(),
  promotion_max_duration_days: yup.number().integer().required(),
  maximum_active_promotions: yup.number().integer().required(),
  featured_listing_cost_usd: yup.number().required(),
  allow_eba_currency: yup.boolean(),
  allow_usd_currency: yup.boolean(),
  allow_both_options: yup.boolean(),
});

const AdminPromotionsAndSponsorshipsListPage = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageLimit, setPageLimit] = useState(10);
  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean;
    id: number | null;
  }>({
    isOpen: false,
    id: null,
  });

  // React Query hooks
  const {
    data: settingsData,
    isLoading: settingsLoading,
    error: settingsError,
  } = usePromotionSettingsQuery();
  const {
    data: listingsData,
    isLoading: listingsLoading,
    error: listingsError,
  } = useSponsoredListingsQuery(currentPage, pageLimit);
  const { mutate: updateSettings, isPending: isUpdating } =
    useUpdatePromotionSettingsMutation();
  const { mutate: deleteSponsoredListing, isPending: isDeleting } =
    useDeleteSponsoredListingMutation();

  // Extract data from queries
  const settings = settingsData?.data;
  const sponsoredListings = listingsData?.data || [];
  const pagination = (listingsData as any)?.pagination;

  // Debug logging
  console.log("🔍 Debug - settingsData:", settingsData);
  console.log("🔍 Debug - settings:", settings);
  console.log("🔍 Debug - settingsLoading:", settingsLoading);

  const {
    register,
    handleSubmit,
    watch,
    reset,
    setValue,
    formState: { errors },
    getValues,
  } = useForm<IPromotionSettings>({
    resolver: yupResolver(promotionSettingsSchema),
    mode: "onChange", // Ensure form updates immediately
    defaultValues: {
      featured_listing_cost_eba: 0,
      promotion_max_duration_days: 30,
      maximum_active_promotions: 10,
      featured_listing_cost_usd: 0,
      allow_eba_currency: true,
      allow_usd_currency: false,
      allow_both_options: false,
    },
  });

  // Debug: Log current form values
  const currentFormValues = watch();
  React.useEffect(() => {
    console.log("🔍 Current form values changed:", currentFormValues);
  }, [currentFormValues]);

  console.log("🔍 Debug - settings:", settings);
  console.log("🔍 Debug - settingsData:", settingsData);

  // Force form reset when settings are available
  React.useEffect(() => {
    console.log(
      "🔄 useEffect triggered - settings:",
      !!settings,
      "loading:",
      settingsLoading,
      "settingsData:",
      settingsData
    );

    // Only reset form when we have settings data and loading is complete
    if (settings && !settingsLoading) {
      console.log("🔄 Resetting form with settings:", settings);

      // Convert boolean values properly (database stores as 0/1 or boolean)
      const formData = {
        featured_listing_cost_eba:
          Number(settings.featured_listing_cost_eba) || 0,
        promotion_max_duration_days:
          Number(settings.promotion_max_duration_days) || 30,
        maximum_active_promotions:
          Number(settings.maximum_active_promotions) || 10,
        featured_listing_cost_usd:
          Number(settings.featured_listing_cost_usd) || 0,
        allow_eba_currency: Boolean(settings.allow_eba_currency),
        allow_usd_currency: Boolean(settings.allow_usd_currency),
        allow_both_options: Boolean(settings.allow_both_options),
      };

      console.log("📝 Form data being set:", formData);

      // Try both reset and individual setValue calls
      reset(formData);

      // Also set values individually as a fallback
      Object.entries(formData).forEach(([key, value]) => {
        setValue(key as keyof IPromotionSettings, value, {
          shouldValidate: false,
          shouldDirty: false,
          shouldTouch: false,
        });
      });

      // Debug: Check if reset worked
      setTimeout(() => {
        const valuesAfterReset = getValues();
        console.log("📝 Form values after reset:", valuesAfterReset);
        console.log(
          "📝 Reset successful?",
          JSON.stringify(valuesAfterReset) !==
            JSON.stringify({
              featured_listing_cost_eba: 0,
              promotion_max_duration_days: 30,
              maximum_active_promotions: 10,
              featured_listing_cost_usd: 0,
              allow_eba_currency: true,
              allow_usd_currency: false,
              allow_both_options: false,
            })
        );
      }, 100);
    }
  }, [settings, settingsLoading, settingsData, reset, setValue, getValues]);

  // Separate effect to log when data changes
  React.useEffect(() => {
    console.log("📊 Data changed - settingsData:", settingsData);
  }, [settingsData]);

  const onSubmit: SubmitHandler<IPromotionSettings> = (data) => {
    console.log("🚀 Form submitted with data:", data);
    console.log("🔧 updateSettings function:", updateSettings);

    // Remove timestamp fields that shouldn't be sent to backend
    const { created_at, updated_at, id, ...cleanData } = data as any;
    console.log("🧹 Cleaned data (without timestamps):", cleanData);

    updateSettings(cleanData);
  };

  const handleDelete = () => {
    if (deleteModal.id) {
      console.log("🗑️ Deleting sponsored listing with ID:", deleteModal.id);
      console.log(
        "🔧 deleteSponsoredListing function:",
        deleteSponsoredListing
      );
      deleteSponsoredListing(deleteModal.id);
      setDeleteModal({ isOpen: false, id: null });
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const columns = [
    { header: "Listing ID", accessor: "listing_id" },
    { header: "Title", accessor: "title" },
    { header: "Seller", accessor: "seller" },
    { header: "Start Date", accessor: "start_date" },
    { header: "End Date", accessor: "end_date" },
    { header: "Status", accessor: "status" },
    { header: "Actions", accessor: "actions" },
  ];

  return (
    <AdminWrapper>
      <div className="p-6 bg-[#F7F8FA] min-h-screen">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-[#1E293B]">
              Promotions & Sponsorships
            </h1>
            <p className="text-[#718096]">
              Manage featured listings and promotional content
            </p>
          </div>
          <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
            <UserIcon className="w-6 h-6 text-gray-500" />
          </div>
        </div>

        <form
          onSubmit={handleSubmit(onSubmit)}
          className="bg-white p-8 rounded-lg shadow-md mb-8"
        >
          <h2 className="text-xl font-bold mb-6 text-[#1A202C]">
            Promotion Settings
          </h2>
          {settingsLoading || !settings ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {[...Array(4)].map((_, i) => (
                <Skeleton key={i} className="h-16" />
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <MkdInputV2
                key={`eba-cost-${settings?.id || "default"}`}
                type="number"
                {...register("featured_listing_cost_eba")}
                errors={errors.featured_listing_cost_eba}
              >
                <MkdInputV2Label>
                  Featured Listing cost per day (EBA$)
                </MkdInputV2Label>
                <MkdInputV2Field />
              </MkdInputV2>
              <MkdInputV2
                key={`max-duration-${settings?.id || "default"}`}
                type="number"
                {...register("promotion_max_duration_days")}
                errors={errors.promotion_max_duration_days}
              >
                <MkdInputV2Label>Promotion Max Duration (Days)</MkdInputV2Label>
                <MkdInputV2Field />
              </MkdInputV2>
              <MkdInputV2
                key={`max-promotions-${settings?.id || "default"}`}
                type="number"
                {...register("maximum_active_promotions")}
                errors={errors.maximum_active_promotions}
              >
                <MkdInputV2Label>Maximum Active Promotions</MkdInputV2Label>
                <MkdInputV2Field />
              </MkdInputV2>
              <MkdInputV2
                key={`usd-cost-${settings?.id || "default"}`}
                type="number"
                {...register("featured_listing_cost_usd")}
                errors={errors.featured_listing_cost_usd}
              >
                <MkdInputV2Label>
                  Featured Listing cost per day (USD$)
                </MkdInputV2Label>
                <MkdInputV2Field />
              </MkdInputV2>
            </div>
          )}
          <div className="mt-8">
            <h3 className="text-lg font-bold mb-4 text-[#1A202C]">
              Currency Options
            </h3>
            <div className="flex flex-col space-y-4">
              <div className="flex items-center">
                <input
                  key={`eba-currency-${settings?.id || "default"}`}
                  type="checkbox"
                  id="eba_currency"
                  className="h-4 w-4 text-[#E63946] border-gray-300 rounded focus:ring-[#E63946]"
                  {...register("allow_eba_currency")}
                />
                <label
                  htmlFor="eba_currency"
                  className="ml-3 text-sm text-[#2D3748]"
                >
                  eBa$ (Platform Currency)
                </label>
              </div>
              <div className="flex items-center">
                <input
                  key={`usd-currency-${settings?.id || "default"}`}
                  type="checkbox"
                  id="usd_currency"
                  className="h-4 w-4 text-[#E63946] border-gray-300 rounded focus:ring-[#E63946]"
                  {...register("allow_usd_currency")}
                />
                <label
                  htmlFor="usd_currency"
                  className="ml-3 text-sm text-[#2D3748]"
                >
                  USD (Real Currency)
                </label>
              </div>
              <div className="flex items-center">
                <input
                  key={`allow-both-${settings?.id || "default"}`}
                  type="checkbox"
                  id="allow_both"
                  className="h-4 w-4 text-[#E63946] border-gray-300 rounded focus:ring-[#E63946]"
                  {...register("allow_both_options")}
                />
                <label
                  htmlFor="allow_both"
                  className="ml-3 text-sm text-[#2D3748]"
                >
                  Allow both payment options
                </label>
              </div>
            </div>
          </div>
          <div className="flex justify-end mt-8">
            <InteractiveButton
              type="submit"
              className="!bg-[#0F2C59] !hover:bg-[#0F2C59]/90"
              loading={isUpdating}
            >
              Save Settings
            </InteractiveButton>
          </div>
        </form>

        <div className="bg-white p-8 rounded-lg shadow-md">
          <h2 className="text-xl font-bold mb-6 text-[#1A202C]">
            Sponsored Listings
          </h2>
          <div className="overflow-x-auto">
            {listingsLoading ? (
              <MkdLoader />
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    {columns.map((column) => (
                      <th
                        key={column.accessor}
                        scope="col"
                        className="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider"
                      >
                        {column.header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {(sponsoredListings || []).map(
                    (item: ISponsoredListing, index: number) => (
                      <tr key={item.id || index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {item.listing_id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {item.title}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {item.seller}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {item.start_date}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {item.end_date}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className="bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full">
                            {item.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() =>
                              setDeleteModal({ isOpen: true, id: item.id })
                            }
                            className="text-[#E63946] hover:underline"
                            disabled={isDeleting}
                          >
                            {isDeleting ? "Deleting..." : "Delete"}
                          </button>
                        </td>
                      </tr>
                    )
                  )}
                </tbody>
              </table>
            )}
          </div>
          {pagination && (
            <PaginationBar
              currentPage={(pagination as IPagination).page}
              pageCount={(pagination as IPagination).num_pages}
              pageSize={(pagination as IPagination).limit}
              canPreviousPage={(pagination as IPagination).has_prev}
              canNextPage={(pagination as IPagination).has_next}
              updatePageSize={() => {}}
              updateCurrentPage={handlePageChange}
              canChangeLimit={false}
              startSize={10}
              multiplier={10}
            />
          )}
        </div>
      </div>
      {deleteModal.isOpen && (
        <ActionConfirmationModal
          isOpen={deleteModal.isOpen}
          onClose={() => setDeleteModal({ isOpen: false, id: null })}
          onSuccess={handleDelete}
          title="Confirm Deletion"
          customMessage="Are you sure you want to delete this sponsored listing? This action cannot be undone."
        />
      )}
    </AdminWrapper>
  );
};

export default AdminPromotionsAndSponsorshipsListPage;
