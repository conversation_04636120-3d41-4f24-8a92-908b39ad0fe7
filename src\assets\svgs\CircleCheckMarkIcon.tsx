interface CircleCheckMarkIconProps {
  className?: string;
  fill?: string;
  stroke?: string;
  onClick?: (e?: any) => void;
}
const CircleCheckMarkIcon = ({
  className = "",
  fill = "#38C793",
  onClick,
}: CircleCheckMarkIconProps) => {
  return (
    <svg
      className={`${className}`}
      onClick={onClick}
      width="12"
      height="12"
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6 12C2.6862 12 0 9.3138 0 6C0 2.6862 2.6862 0 6 0C9.3138 0 12 2.6862 12 6C12 9.3138 9.3138 12 6 12ZM5.4018 8.4L9.6438 4.1574L8.7954 3.309L5.4018 6.7032L3.7044 5.0058L2.856 5.8542L5.4018 8.4Z"
        fill={fill}
      />
    </svg>
  );
};

export default CircleCheckMarkIcon;
