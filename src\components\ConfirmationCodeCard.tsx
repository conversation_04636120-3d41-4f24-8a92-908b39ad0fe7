import React, { useState } from "react";
import { IConfirmationCodeData, formatCodeExpiration, getCodeStatusColor, getCodeStatusText } from "../query/useConfirmationCodes";
import { Skeleton } from "./Skeleton";

interface IConfirmationCodeCardProps {
  type: "pickup" | "delivery";
  data?: IConfirmationCodeData;
  isLoading: boolean;
  error?: Error | null;
  onRefresh?: () => void;
}

export const ConfirmationCodeCard: React.FC<IConfirmationCodeCardProps> = ({
  type,
  data,
  isLoading,
  error,
  onRefresh,
}) => {
  const [codeCopied, setCodeCopied] = useState(false);

  const isPickup = type === "pickup";
  const code = isPickup ? data?.pickupCode : data?.deliveryCode;
  const title = isPickup ? "Pickup Confirmation Code" : "Delivery Confirmation Code";
  const subtitle = isPickup 
    ? "Show this code to the delivery agent when they pick up your item"
    : "Show this code to the delivery agent when they deliver your item";

  const handleCopyCode = async () => {
    if (code) {
      try {
        await navigator.clipboard.writeText(code);
        setCodeCopied(true);
        setTimeout(() => setCodeCopied(false), 2000);
      } catch (err) {
        console.error("Failed to copy code:", err);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg p-6 border border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-20" />
        </div>
        <Skeleton className="h-4 w-full mb-2" />
        <Skeleton className="h-12 w-32 mb-4" />
        <Skeleton className="h-4 w-64" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg p-6 border border-red-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <span className="text-red-600 text-sm font-medium">Error</span>
        </div>
        <p className="text-red-600 mb-4">
          {error.message || `Failed to load ${type} code`}
        </p>
        {onRefresh && (
          <button
            onClick={onRefresh}
            className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        )}
      </div>
    );
  }

  if (!data || !code) {
    return (
      <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <span className="text-gray-500 text-sm font-medium">Not Available</span>
        </div>
        <p className="text-gray-600">
          No {type} code found for this transaction.
        </p>
      </div>
    );
  }

  const statusColor = getCodeStatusColor(data.isExpired, data.isVerified);
  const statusText = getCodeStatusText(data.isExpired, data.isVerified);

  return (
    <div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        <span className={`text-sm font-medium ${statusColor}`}>
          {statusText}
        </span>
      </div>

      {/* Subtitle */}
      <p className="text-gray-600 text-sm mb-4">{subtitle}</p>

      {/* Code Display */}
      <div className="bg-gray-50 rounded-lg p-4 mb-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-xs text-gray-500 uppercase tracking-wide mb-1">
              {type} Code
            </p>
            <p className="text-2xl font-mono font-bold text-gray-900 tracking-wider">
              {code}
            </p>
          </div>
          <button
            onClick={handleCopyCode}
            className="bg-[#0D3166] text-white px-3 py-2 rounded-md hover:bg-[#1a4480] transition-colors text-sm"
          >
            {codeCopied ? "Copied!" : "Copy"}
          </button>
        </div>
      </div>

      {/* Status Information */}
      <div className="space-y-2 text-sm">
        {!data.isExpired && !data.isVerified && (
          <div className="flex justify-between">
            <span className="text-gray-600">Expires:</span>
            <span className="text-orange-600 font-medium">
              {formatCodeExpiration(data.expiresAt)}
            </span>
          </div>
        )}

        {data.isVerified && data.verifiedAt && (
          <div className="flex justify-between">
            <span className="text-gray-600">Verified:</span>
            <span className="text-green-600 font-medium">
              {new Date(data.verifiedAt).toLocaleString()}
            </span>
          </div>
        )}

        {data.deliveryAgent && (
          <div className="flex justify-between">
            <span className="text-gray-600">Delivery Agent:</span>
            <span className="text-gray-900 font-medium">
              {data.deliveryAgent.name}
            </span>
          </div>
        )}

        {/* Delivery-specific status */}
        {type === "delivery" && data.pickupVerified !== undefined && (
          <div className="flex justify-between">
            <span className="text-gray-600">Pickup Status:</span>
            <span className={data.pickupVerified ? "text-green-600" : "text-gray-500"}>
              {data.pickupVerified ? "Verified" : "Pending"}
            </span>
          </div>
        )}
      </div>

      {/* Instructions */}
      {data.instructions && (
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <p className="text-blue-800 text-sm">
            <span className="font-medium">💡 Tip:</span> {data.instructions}
          </p>
        </div>
      )}

      {/* Warning for expired codes */}
      {data.isExpired && !data.isVerified && (
        <div className="mt-4 p-3 bg-red-50 rounded-lg">
          <p className="text-red-800 text-sm">
            <span className="font-medium">⚠️ Warning:</span> This code has expired. 
            Please contact support if you need assistance.
          </p>
        </div>
      )}
    </div>
  );
};
