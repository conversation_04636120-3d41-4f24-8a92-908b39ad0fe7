import React from "react";
import { Link } from "react-router-dom";

const LandingHeader = () => {
  return (
    <header className="bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <span className="text-2xl font-bold">
                <span style={{ color: "#F52D2A" }}>eBa</span>
                <span className="text-gray-900">Dollar</span>
              </span>
            </div>
          </div>

          {/* Navigation Menu */}
          <nav className="hidden md:flex space-x-8">
            <a
              href="#how-it-works"
              className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
            >
              How It Works
            </a>
            <a
              href="#benefits"
              className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
            >
              Benefits
            </a>
            <a
              href="#faqs"
              className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
            >
              FAQs
            </a>
            <a
              href="#about"
              className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
            >
              About Us
            </a>
          </nav>

          {/* Login and Sign Up Buttons */}
          <div className="flex items-center space-x-4">
            <Link
              to="/member/login"
              className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium"
            >
              Login
            </Link>
            <Link
              to="/member/sign-up"
              className="text-white px-6 py-2 rounded-md text-sm font-medium transition-colors hover:opacity-90"
              style={{ backgroundColor: "#F52D2A" }}
            >
              Sign Up
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              type="button"
              className="text-gray-700 hover:text-gray-900 focus:outline-none focus:text-gray-900"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default LandingHeader;
