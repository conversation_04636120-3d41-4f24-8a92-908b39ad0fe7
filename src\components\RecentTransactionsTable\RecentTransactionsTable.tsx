import React from "react";
import { Link } from "react-router-dom";

export interface Transaction {
  id: number;
  date: string;
  description: string;
  type: string;
  amount: number;
  status: string;
}

export interface RecentTransactionsTableProps {
  transactions: Transaction[];
  title?: string;
  viewAllLink?: string;
  formatTransactionAmount?: (amount: number) => string;
  className?: string;
}

const RecentTransactionsTable: React.FC<RecentTransactionsTableProps> = ({
  transactions,
  title = "Recent Transactions",
  viewAllLink = "#",
  formatTransactionAmount = (amount: number) =>
    `${amount >= 0 ? "+" : ""}eBa$${amount.toFixed(2)}`,
  className = "",
}) => {
  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return "bg-green-100 text-green-700";
      case "pending":
        return "bg-yellow-100 text-yellow-700";
      case "failed":
      case "cancelled":
        return "bg-red-100 text-red-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  const formatStatus = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  return (
    <div className={`bg-white rounded-lg shadow p-6 mb-6 ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-gray-800">{title}</h2>
        <Link to={viewAllLink} className="text-sm text-red-500 hover:underline">
          View All →
        </Link>
      </div>

      {transactions.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <div className="text-4xl mb-2">📊</div>
          <p>No recent transactions</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full text-left text-sm text-gray-600">
            <thead className="border-b">
              <tr>
                <th className="p-2 font-semibold">Date</th>
                <th className="p-2 font-semibold">Description</th>
                <th className="p-2 font-semibold">Type</th>
                <th className="p-2 font-semibold">Amount</th>
                <th className="p-2 font-semibold">Status</th>
              </tr>
            </thead>
            <tbody>
              {transactions.map((transaction) => (
                <tr
                  key={transaction.id}
                  className="border-b hover:bg-gray-50 transition-colors"
                >
                  <td className="p-2">
                    {new Date(transaction.date).toLocaleDateString()}
                  </td>
                  <td className="p-2 font-medium">{transaction.description}</td>
                  <td className="p-2">
                    <span className="capitalize">{transaction.type}</span>
                  </td>
                  <td
                    className={`p-2 font-semibold ${
                      transaction.amount >= 0
                        ? "text-green-500"
                        : "text-red-500"
                    }`}
                  >
                    {formatTransactionAmount(transaction.amount)}
                  </td>
                  <td className="p-2">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(
                        transaction.status
                      )}`}
                    >
                      {formatStatus(transaction.status)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default RecentTransactionsTable;
