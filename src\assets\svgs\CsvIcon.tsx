interface CsvIconProps {
  className?: string;
  fill?: string;
  stroke?: string;
  onClick?: (e?: any) => void;
}

const CsvIcon = ({
  className = "",
  fill = "#38C793",
  stroke = "#D4D4D8",
  onClick,
}: CsvIconProps) => {
  return (
    <svg
      className={`${className}`}
      onClick={onClick}
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_4112_120787)">
        <g filter="url(#filter0_di_4112_120787)">
          <path
            d="M30 40H10C6.68629 40 4 37.3137 4 34V6C4 2.68629 6.68629 0 10 0H20.5147C22.106 0 23.6321 0.632142 24.7574 1.75736L34.2426 11.2426C35.3679 12.3679 36 13.894 36 15.4853V34C36 37.3137 33.3137 40 30 40Z"
            fill="white"
          />
          <path
            d="M30 39.25H10C7.10051 39.25 4.75 36.8995 4.75 34V6C4.75 3.10051 7.10051 0.75 10 0.75H20.5147C21.9071 0.75 23.2425 1.30312 24.227 2.28769L33.7123 11.773C34.6969 12.7575 35.25 14.0929 35.25 15.4853V34C35.25 36.8995 32.8995 39.25 30 39.25Z"
            stroke={stroke}
            strokeWidth="1.5"
          />
        </g>
        <path
          d="M23 1V9C23 11.2091 24.7909 13 27 13H35"
          stroke={stroke}
          strokeWidth="1.5"
        />
        <g filter="url(#filter1_i_4112_120787)">
          <mask id="path-4-inside-1_4112_120787" fill="white">
            <path d="M0 22C0 19.7909 1.79086 18 4 18H26C28.2091 18 30 19.7909 30 22V30C30 32.2091 28.2091 34 26 34H4C1.79086 34 0 32.2091 0 30V22Z" />
          </mask>
          <path
            d="M0 22C0 19.7909 1.79086 18 4 18H26C28.2091 18 30 19.7909 30 22V30C30 32.2091 28.2091 34 26 34H4C1.79086 34 0 32.2091 0 30V22Z"
            fill={fill}
          />
          <path
            d="M-1 22C-1 19.2386 1.23858 17 4 17H26C28.7614 17 31 19.2386 31 22H29C29 20.3431 27.6569 19 26 19H4C2.34315 19 1 20.3431 1 22H-1ZM30 34H0H30ZM4 34C1.23858 34 -1 31.7614 -1 29V22C-1 19.2386 1.23858 17 4 17V19C2.34315 19 1 20.3431 1 22V30C1 32.2091 2.34315 34 4 34ZM26 17C28.7614 17 31 19.2386 31 22V29C31 31.7614 28.7614 34 26 34C27.6569 34 29 32.2091 29 30V22C29 20.3431 27.6569 19 26 19V17Z"
            fill="black"
            fillOpacity="0.08"
            mask="url(#path-4-inside-1_4112_120787)"
          />
          <path
            d="M10.6367 24.6992H9.17578C9.13411 24.4596 9.05729 24.2474 8.94531 24.0625C8.83333 23.875 8.69401 23.7161 8.52734 23.5859C8.36068 23.4557 8.17057 23.3581 7.95703 23.293C7.74609 23.2253 7.51823 23.1914 7.27344 23.1914C6.83854 23.1914 6.45313 23.3008 6.11719 23.5195C5.78125 23.7357 5.51823 24.0534 5.32813 24.4727C5.13802 24.8893 5.04297 25.3984 5.04297 26C5.04297 26.612 5.13802 27.1276 5.32813 27.5469C5.52083 27.9635 5.78385 28.2786 6.11719 28.4922C6.45313 28.7031 6.83724 28.8086 7.26953 28.8086C7.50911 28.8086 7.73307 28.7773 7.94141 28.7148C8.15234 28.6497 8.34115 28.5547 8.50781 28.4297C8.67708 28.3047 8.81901 28.151 8.93359 27.9687C9.05078 27.7865 9.13151 27.5781 9.17578 27.3437L10.6367 27.3516C10.582 27.7318 10.4635 28.0885 10.2812 28.4219C10.1016 28.7552 9.86589 29.0495 9.57422 29.3047C9.28255 29.5573 8.94141 29.7552 8.55078 29.8984C8.16016 30.0391 7.72656 30.1094 7.25 30.1094C6.54688 30.1094 5.91927 29.9466 5.36719 29.6211C4.8151 29.2956 4.38021 28.8255 4.0625 28.2109C3.74479 27.5964 3.58594 26.8594 3.58594 26C3.58594 25.138 3.74609 24.401 4.06641 23.7891C4.38672 23.1745 4.82292 22.7044 5.375 22.3789C5.92708 22.0534 6.55208 21.8906 7.25 21.8906C7.69531 21.8906 8.10938 21.9531 8.49219 22.0781C8.875 22.2031 9.21615 22.3867 9.51563 22.6289C9.8151 22.8685 10.0612 23.1628 10.2539 23.5117C10.4492 23.8581 10.5768 24.2539 10.6367 24.6992ZM16.5315 24.1992C16.4951 23.8581 16.3414 23.5924 16.0706 23.4023C15.8024 23.2122 15.4534 23.1172 15.0237 23.1172C14.7216 23.1172 14.4625 23.1628 14.2464 23.2539C14.0302 23.3451 13.8649 23.4687 13.7503 23.625C13.6357 23.7812 13.5771 23.9596 13.5745 24.1602C13.5745 24.3268 13.6123 24.4714 13.6878 24.5937C13.7659 24.7161 13.8714 24.8203 14.0042 24.9062C14.137 24.9896 14.2841 25.0599 14.4456 25.1172C14.607 25.1745 14.7698 25.2227 14.9339 25.2617L15.6839 25.4492C15.986 25.5195 16.2763 25.6146 16.555 25.7344C16.8362 25.8542 17.0875 26.0052 17.3089 26.1875C17.5328 26.3698 17.7099 26.5898 17.8401 26.8477C17.9703 27.1055 18.0354 27.4076 18.0354 27.7539C18.0354 28.2227 17.9156 28.6354 17.6761 28.9922C17.4365 29.3464 17.0901 29.6237 16.637 29.8242C16.1865 30.0221 15.6409 30.1211 15.0003 30.1211C14.3779 30.1211 13.8375 30.0247 13.3792 29.832C12.9235 29.6393 12.5667 29.3581 12.3089 28.9883C12.0537 28.6185 11.9156 28.168 11.8948 27.6367H13.3206C13.3414 27.9154 13.4274 28.1471 13.5784 28.332C13.7294 28.5169 13.9261 28.6549 14.1682 28.7461C14.413 28.8372 14.6865 28.8828 14.9886 28.8828C15.3037 28.8828 15.5797 28.8359 15.8167 28.7422C16.0563 28.6458 16.2438 28.513 16.3792 28.3437C16.5146 28.1719 16.5836 27.9714 16.5862 27.7422C16.5836 27.5339 16.5224 27.362 16.4026 27.2266C16.2828 27.0885 16.1149 26.974 15.8987 26.8828C15.6852 26.7891 15.4352 26.7057 15.1487 26.6328L14.2386 26.3984C13.5797 26.2292 13.0589 25.9727 12.6761 25.6289C12.2958 25.2826 12.1057 24.8229 12.1057 24.25C12.1057 23.7786 12.2333 23.3659 12.4886 23.0117C12.7464 22.6576 13.0966 22.3828 13.5393 22.1875C13.982 21.9896 14.4833 21.8906 15.0432 21.8906C15.611 21.8906 16.1083 21.9896 16.5354 22.1875C16.9651 22.3828 17.3024 22.6549 17.5471 23.0039C17.7919 23.3503 17.9182 23.7487 17.9261 24.1992H16.5315ZM20.6177 22L22.6998 28.2969H22.7818L24.8599 22H26.4537L23.6334 30H21.8443L19.0279 22H20.6177Z"
            fill="white"
          />
        </g>
      </g>
      <defs>
        <filter
          id="filter0_di_4112_120787"
          x="2"
          y="-4"
          width="36"
          height="47"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.894118 0 0 0 0 0.898039 0 0 0 0 0.905882 0 0 0 0.24 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_4112_120787"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_4112_120787"
            result="shape"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="-4" />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.813571 0 0 0 0 0.82 0 0 0 0 0.826429 0 0 0 0.32 0"
          />
          <feBlend
            mode="normal"
            in2="shape"
            result="effect2_innerShadow_4112_120787"
          />
        </filter>
        <filter
          id="filter1_i_4112_120787"
          x="0"
          y="18"
          width="30"
          height="20"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="2" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.08 0"
          />
          <feBlend
            mode="normal"
            in2="shape"
            result="effect1_innerShadow_4112_120787"
          />
        </filter>
        <clipPath id="clip0_4112_120787">
          <rect width="40" height="40" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default CsvIcon;
