import React from "react";
import { useDebugComplaints } from "../hooks/useDebugComplaints";

export const DebugComplaints: React.FC = () => {
  const { complaints, loading, error } = useDebugComplaints();

  if (loading) return <div>Loading complaints...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div style={{ 
      padding: "20px", 
      border: "2px solid #ff0000", 
      margin: "20px",
      backgroundColor: "#fff3cd"
    }}>
      <h3>🐛 Debug: Available Complaint IDs</h3>
      <p><strong>Total complaints found:</strong> {complaints.length}</p>
      
      {complaints.length === 0 ? (
        <div style={{ color: "red" }}>
          <p>❌ No complaints found in database!</p>
          <p>You need to create some test data first.</p>
        </div>
      ) : (
        <div>
          <p>✅ Available complaint IDs:</p>
          <ul>
            {complaints.map((complaint) => (
              <li key={complaint.id}>
                <strong>ID {complaint.id}</strong> - 
                Customer: {complaint.customer_id}, 
                Agent: {complaint.delivery_agent_id}, 
                Created: {new Date(complaint.created_at).toLocaleDateString()}
              </li>
            ))}
          </ul>
          <p style={{ color: "green" }}>
            💡 Try using one of these IDs in your URL instead of ID 4
          </p>
        </div>
      )}
    </div>
  );
};
