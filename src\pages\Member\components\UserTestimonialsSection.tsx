import React from "react";
import { Skeleton } from "../../../components/Skeleton";
import { useTestimonialsQuery } from "../../../query/useLanding";

const UserTestimonialsSection = () => {
  const {
    data: testimonialsData,
    isLoading,
    error,
  } = useTestimonialsQuery(true); // Get featured testimonials

  const testimonials = testimonialsData?.data || [];

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <svg
        key={index}
        className="w-4 h-4"
        fill={index < rating ? "#F52D2A" : "#E5E7EB"}
        viewBox="0 0 24 24"
      >
        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
      </svg>
    ));
  };

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2
            className="text-3xl md:text-4xl font-bold"
            style={{ color: "#0D3166" }}
          >
            User Testimonials
          </h2>
        </div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {isLoading ? (
            // Loading skeleton
            [...Array(2)].map((_, index) => (
              <div
                key={index}
                className="bg-white rounded-lg p-6 shadow-sm border border-gray-100"
              >
                <div className="flex items-center mb-4">
                  <Skeleton className="w-10 h-10 rounded-full mr-3" />
                  <div>
                    <Skeleton className="h-4 w-24 mb-2" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                </div>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            ))
          ) : error ? (
            <div className="col-span-2 text-center py-8">
              <div className="text-red-500 mb-4">
                Failed to load testimonials
              </div>
              <button
                onClick={() => window.location.reload()}
                className="bg-[#0D3166] text-white px-4 py-2 rounded-md hover:bg-[#1a4480]"
              >
                Retry
              </button>
            </div>
          ) : testimonials.length === 0 ? (
            <div className="col-span-2 text-center py-8 text-gray-500">
              No testimonials available
            </div>
          ) : (
            testimonials.map((testimonial: any) => (
              <div
                key={testimonial.id}
                className="bg-white rounded-lg p-6 shadow-sm border border-gray-100"
              >
                {/* User Info */}
                <div className="flex items-center mb-4">
                  <img
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    className="w-10 h-10 rounded-full mr-3"
                  />
                  <div>
                    <h4 className="font-semibold text-gray-900">
                      {testimonial.name}
                    </h4>
                    {testimonial.title && (
                      <p className="text-xs text-gray-500">
                        {testimonial.title}
                      </p>
                    )}
                    <div className="flex items-center mt-1">
                      {renderStars(testimonial.rating)}
                    </div>
                  </div>
                </div>

                {/* Testimonial Text */}
                <p className="text-gray-600 text-sm leading-relaxed">
                  "{testimonial.testimonial}"
                </p>
              </div>
            ))
          )}
        </div>
      </div>
    </section>
  );
};

export default UserTestimonialsSection;
