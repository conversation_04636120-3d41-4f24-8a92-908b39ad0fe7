import React from "react";
import { AdminWrapper } from "../../../components/AdminWrapper";
import { InteractiveButton } from "../../../components/InteractiveButton";
import { FaStar } from "react-icons/fa";
import { IoMdCheckmarkCircleOutline } from "react-icons/io";
import { MdOutlineCancel } from "react-icons/md";
import { useDeliveryAgentComplaintDetails } from "../../../hooks/useDeliveryAgentComplaintDetails";
import { MkdLoader } from "../../../components/MkdLoader";
import { useContexts } from "../../../hooks/useContexts";
import { useEffect } from "react";
import moment from "moment";

const AdminDeliveryAgentComplaintDetailsPage = () => {
  const {
    complaints,
    loading,
    error,
    updateComplaintStatus,
    suspendDeliveryAgent,
    agentId,
  } = useDeliveryAgentComplaintDetails();
  const { globalDispatch } = useContexts();

  useEffect(() => {
    if (error) {
      globalDispatch({
        type: "SNACKBAR",
        payload: {
          message: error.message,
          toastStatus: "error",
        },
      });
    }
  }, [error, globalDispatch]);

  if (loading) {
    return (
      <AdminWrapper>
        <div className="flex justify-center items-center h-screen">
          <MkdLoader />
        </div>
      </AdminWrapper>
    );
  }

  if (error) {
    return (
      <AdminWrapper>
        <div className="text-center p-6">
          <p>Could not load complaint details.</p>
        </div>
      </AdminWrapper>
    );
  }

  if (!complaints || complaints.length === 0) {
    return (
      <AdminWrapper>
        <div className="text-center p-6">
          <p>No complaints found for this agent.</p>
        </div>
      </AdminWrapper>
    );
  }

  const getStatusInfo = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return { color: "#f59e0b", text: "Under Review" };
      case "resolved":
      case "valid":
        return { color: "#10b981", text: "Resolved" };
      case "invalid":
        return { color: "#ef4444", text: "Dismissed" };
      case "in_review":
        return { color: "#3b82f6", text: "In Review" };
      default:
        return { color: "#6b7280", text: "Unknown" };
    }
  };

  // Get agent info from first complaint (all complaints are for the same agent)
  const agentInfo = complaints[0]?.agent;

  // Debug: Log the complaints data structure
  console.log("Complaints data:", complaints);

  return (
    <AdminWrapper>
      <div className="p-6 bg-gray-50 min-h-screen">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">
              Delivery Agent Complaint Details
            </h1>
            <p className="text-gray-500 mt-1">
              Review individual complaints and agent responses
            </p>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow mb-6 flex items-center justify-between">
          <div className="flex items-center">
            <img
              src={
                agentInfo?.avatar ||
                `https://i.pravatar.cc/150?u=${agentInfo?.id || agentId}`
              }
              alt={agentInfo?.name || "Agent"}
              className="w-20 h-20 rounded-full"
            />
            <div className="ml-6">
              <h2 className="text-2xl font-bold text-gray-800">
                {String(agentInfo?.name || "Unknown Agent")}
              </h2>
              <p className="text-gray-500">
                Agent ID: {String(agentInfo?.id || agentId)}
              </p>
              <div className="flex items-center mt-1">
                <span className="text-gray-500">
                  Total Complaints: {complaints.length}
                </span>
              </div>
            </div>
          </div>
          <InteractiveButton
            className="bg-[#E63946] text-white"
            onClick={suspendDeliveryAgent}
          >
            Suspend User
          </InteractiveButton>
        </div>

        {/* Display all complaints for this agent */}
        {complaints.map((complaint, _index) => {
          const statusInfo = getStatusInfo(complaint.status);

          return (
            <div
              key={complaint.id}
              className="bg-white rounded-lg shadow p-6 mb-6"
            >
              <div className="flex justify-between items-center mb-4">
                <div className="flex items-center">
                  <span
                    className={`px-3 py-1 text-sm rounded-full text-white`}
                    style={{ backgroundColor: statusInfo.color }}
                  >
                    {statusInfo.text}
                  </span>
                  <span className="ml-4 text-gray-600">
                    Complaint ID: #{complaint.id}
                  </span>
                </div>
                <span className="text-gray-500 text-sm">
                  {moment(complaint.created_at).format("MMM DD, YYYY")}
                </span>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h3 className="font-semibold text-gray-800 mb-2">
                    Listing Information
                  </h3>
                  <div className="flex items-center">
                    <img
                      src={
                        (typeof complaint.listing?.image === "string"
                          ? complaint.listing.image
                          : null) || "https://i.pravatar.cc/150?u=vintacamera"
                      }
                      alt={complaint.listing?.title || "Listing"}
                      className="w-12 h-12 rounded-md mr-4"
                    />
                    <div>
                      <p className="font-medium">
                        {String(complaint.listing?.title || "Unknown Listing")}
                      </p>
                      <p className="text-sm text-gray-500">
                        ID: #{String(complaint.listing?.id || "N/A")}
                      </p>
                    </div>
                  </div>
                  <h3 className="font-semibold text-gray-800 mt-4 mb-2">
                    Complainant
                  </h3>
                  <div className="flex items-center">
                    <img
                      src={
                        complaint.complainant?.avatar ||
                        `https://i.pravatar.cc/150?u=${complaint.complainant?.id}`
                      }
                      alt={complaint.complainant?.name || "Customer"}
                      className="w-10 h-10 rounded-full mr-3"
                    />
                    <div>
                      <p className="font-medium">
                        {String(
                          complaint.complainant?.name || "Unknown Customer"
                        )}
                      </p>
                      <p className="text-sm text-gray-500">
                        {String(complaint.complainant?.email || "No email")}
                      </p>
                    </div>
                  </div>
                  <h3 className="font-semibold text-gray-800 mt-4 mb-2">
                    Buyer's Rating
                  </h3>
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <FaStar
                        key={i}
                        color={
                          i < (complaint.rating || 0) ? "#ffc107" : "#e4e5e9"
                        }
                        size={20}
                      />
                    ))}
                    <span className="ml-2 text-gray-600">
                      {complaint.rating || 0}/5
                    </span>
                  </div>
                </div>
                <div className="col-span-2">
                  <h3 className="font-semibold text-gray-800 mb-2">
                    Complaint Summary
                  </h3>
                  <p className="bg-gray-100 p-4 rounded-lg text-gray-700 mb-4">
                    "{String(complaint.complaint_text || "No complaint text")}"
                  </p>
                  <h3 className="font-semibold text-gray-800 mb-2">
                    Agent's Response
                  </h3>
                  <p className="bg-blue-50 p-4 rounded-lg text-gray-700">
                    "{String(complaint.agent_response || "No response yet")}"
                  </p>
                </div>
              </div>
              <div className="flex justify-end mt-6">
                {complaint.status === "pending" ||
                complaint.status === "in_review" ? (
                  <div className="flex space-x-4">
                    <InteractiveButton
                      className="bg-green-500 text-white flex items-center"
                      onClick={() =>
                        updateComplaintStatus(complaint.id, "valid")
                      }
                    >
                      <IoMdCheckmarkCircleOutline className="mr-2" />
                      Mark as Valid
                    </InteractiveButton>
                    <InteractiveButton
                      className="bg-[#E63946] text-white flex items-center"
                      onClick={() =>
                        updateComplaintStatus(complaint.id, "invalid")
                      }
                    >
                      <MdOutlineCancel className="mr-2" />
                      Mark as Invalid
                    </InteractiveButton>
                  </div>
                ) : (
                  <div className="text-green-600 flex items-center font-semibold">
                    <IoMdCheckmarkCircleOutline className="mr-2" size={20} />
                    Marked as {complaint.status}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </AdminWrapper>
  );
};

export default AdminDeliveryAgentComplaintDetailsPage;
