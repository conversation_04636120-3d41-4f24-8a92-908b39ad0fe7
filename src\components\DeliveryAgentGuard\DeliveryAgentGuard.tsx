import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useUserProfile } from "../../hooks/useUserProfile";
import { MkdLoader } from "../MkdLoader";

interface DeliveryAgentGuardProps {
  children: React.ReactNode;
  redirectTo?: string;
  showMessage?: boolean;
}

export const DeliveryAgentGuard: React.FC<DeliveryAgentGuardProps> = ({
  children,
  redirectTo = "/member/dashboard",
  showMessage = true,
}) => {
  const navigate = useNavigate();
  const { isApprovedDeliveryAgent, loading, deliveryAgentStatus } = useUserProfile();

  useEffect(() => {
    if (!loading && !isApprovedDeliveryAgent) {
      // Redirect non-delivery agents to the specified page
      navigate(redirectTo, { replace: true });
    }
  }, [loading, isApprovedDeliveryAgent, navigate, redirectTo]);

  // Show loading while checking permissions
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <MkdLoader />
      </div>
    );
  }

  // Show access denied message if not a delivery agent and showMessage is true
  if (!isApprovedDeliveryAgent && showMessage) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="max-w-md mx-auto text-center p-8 bg-white rounded-lg shadow-lg">
          <div className="mb-4">
            <span className="text-6xl">🚫</span>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Access Restricted
          </h2>
          <p className="text-gray-600 mb-6">
            {deliveryAgentStatus?.status === "pending"
              ? "Your delivery agent application is currently under review. You'll be able to access delivery features once approved."
              : deliveryAgentStatus?.status === "rejected"
              ? "Your delivery agent application was not approved. Please contact support for more information."
              : "This page is only available to approved delivery agents. Please apply to become a delivery agent first."}
          </p>
          <button
            onClick={() => navigate("/member/dashboard")}
            className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Go to Dashboard
          </button>
        </div>
      </div>
    );
  }

  // If user is an approved delivery agent, render the children
  if (isApprovedDeliveryAgent) {
    return <>{children}</>;
  }

  // Fallback - should not reach here due to useEffect redirect
  return null;
};

export default DeliveryAgentGuard;
