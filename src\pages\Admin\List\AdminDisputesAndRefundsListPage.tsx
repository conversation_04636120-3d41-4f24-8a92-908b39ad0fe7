import React, { useEffect, useState } from "react";
import { AdminWrapper } from "../../../components/AdminWrapper";
import MkdInputV2 from "../../../components/MkdInputV2";
import { InteractiveButton } from "../../../components/InteractiveButton";
import {
  CameraIcon,
  CheckIcon,
  HeadphonesIcon,
  ImageIcon,
  LaptopIcon,
  SmartphoneIcon,
  XIcon,
} from "../../../assets/svgs";
import { Modal } from "../../../components/Modal";
import { useSDK } from "../../../hooks/useSDK";
import { PaginationBar } from "../../../components/PaginationBar";
import { useContexts } from "../../../hooks/useContexts";
import { ToastStatusEnum } from "../../../utils/Enums";
import emptyPageImg from "@/assets/images/empty-page.png";

interface Dispute {
  id: number;
  icon: JSX.Element;
  title: string;
  requester: string;
  reason?: string;
  date: string;
  amount: string;
  status: string;
  images?: string[];
  description?: string;
}

const AdminDisputesAndRefundsListPage = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedDispute, setSelectedDispute] = useState<Dispute | null>(null);
  const [disputes, setDisputes] = useState<Dispute[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<any | null>(null);

  const { sdk } = useSDK({
    project_id: "ebadollar",
  });
  const { showToast } = useContexts();

  const [uiFilters, setUiFilters] = useState({
    search: "",
    status: "all",
    date: "all",
  });

  const [apiParams, setApiParams] = useState({
    search: "",
    status: "all",
    date: "all",
    page: 1,
    limit: 10,
  });

  const handleFilterInputChange = (key: string, value: string | number) => {
    setUiFilters((prev) => ({ ...prev, [key]: value }));
  };

  const handleApplyFilters = () => {
    setApiParams({ ...apiParams, ...uiFilters, page: 1 });
  };

  const handlePageChange = (newPage: number) => {
    setApiParams((prev) => ({ ...prev, page: newPage }));
  };

  const handleLimitChange = (newLimit: number) => {
    setApiParams((prev) => ({ ...prev, limit: newLimit, page: 1 }));
  };

  const fetchDisputes = React.useCallback(async () => {
    setLoading(true);
    try {
      // Mock data that matches the uploaded image
      const mockDisputes = [
        {
          id: 1,
          icon: <CameraIcon className="text-gray-500 w-6 h-6" />,
          title: "Digital camera",
          requester: "John Doe",
          reason: "Not as Described",
          date: "April 23, 2025",
          amount: "300",
          status: "Review",
          images: ["/api/placeholder/200/200", "/api/placeholder/200/200"],
          description:
            "The camera I received does not match the description provided in the listing. The specifications are different and the condition is not as advertised.",
        },
        {
          id: 2,
          icon: <CameraIcon className="text-gray-500 w-6 h-6" />,
          title: "Digital camera",
          requester: "John Doe",
          reason: "Not as Described",
          date: "April 23, 2025",
          amount: "300",
          status: "Funds processed",
          images: ["/api/placeholder/200/200", "/api/placeholder/200/200"],
          description:
            "The camera I received does not match the description provided in the listing.",
        },
        {
          id: 3,
          icon: <CameraIcon className="text-gray-500 w-6 h-6" />,
          title: "Digital camera",
          requester: "John Doe",
          reason: "Not as Described",
          date: "April 23, 2025",
          amount: "300",
          status: "Rejected",
          images: ["/api/placeholder/200/200", "/api/placeholder/200/200"],
          description:
            "The camera I received does not match the description provided in the listing.",
        },
        {
          id: 4,
          icon: <HeadphonesIcon className="text-gray-500 w-6 h-6" />,
          title: "Wireless Headphones",
          requester: "Sarah Wilson",
          reason: "Damaged on Arrival",
          date: "April 22, 2025",
          amount: "150",
          status: "Review",
          images: ["/api/placeholder/200/200", "/api/placeholder/200/200"],
          description:
            "The headphones arrived with visible damage to the casing and do not function properly.",
        },
        {
          id: 5,
          icon: <LaptopIcon className="text-gray-500 w-6 h-6" />,
          title: "Gaming Laptop",
          requester: "Mike Chen",
          reason: "Wrong Item Sent",
          date: "April 21, 2025",
          amount: "1,200",
          status: "Funds processed",
          images: ["/api/placeholder/200/200", "/api/placeholder/200/200"],
          description:
            "I ordered a gaming laptop but received a different model with lower specifications.",
        },
        {
          id: 6,
          icon: <SmartphoneIcon className="text-gray-500 w-6 h-6" />,
          title: "iPhone 14 Pro",
          requester: "Lisa Rodriguez",
          reason: "Battery Issues",
          date: "April 20, 2025",
          amount: "800",
          status: "Review",
          images: ["/api/placeholder/200/200", "/api/placeholder/200/200"],
          description:
            "The iPhone has significant battery issues and does not hold charge as expected.",
        },
      ];

      setDisputes(mockDisputes);
      setPagination({
        page: 1,
        limit: 10,
        total: mockDisputes.length,
        num_pages: 1,
      });

      // Uncomment below for real API call
      /*
      const response = await sdk.callRawAPI(
        "/v2/api/ebadollar/custom/admin/disputes",
        {
          page: apiParams.page,
          limit: apiParams.limit,
          search: apiParams.search,
          status: apiParams.status,
          date: apiParams.date,
        },
        "GET"
      );
      setDisputes(response.data);
      setPagination(response.pagination);
      */
    } catch (error: any) {
      console.error("Error fetching disputes:", error);
      showToast(error.message, 5000, ToastStatusEnum.ERROR);
    } finally {
      setLoading(false);
    }
  }, [apiParams, sdk, showToast]);

  const handleStatusUpdate = async (
    disputeId: number,
    status: "Funds processed" | "Rejected",
    admin_remark?: string
  ) => {
    try {
      await sdk.callRawAPI(
        `/v2/api/ebadollar/custom/admin/disputes/${disputeId}/status`,
        { status, admin_remark },
        "PUT"
      );
      showToast(
        `Dispute status updated to ${status}`,
        5000,
        ToastStatusEnum.SUCCESS
      );
      fetchDisputes(); // Refresh the list
      handleCloseModal();
    } catch (error: any) {
      console.error("Error updating dispute status:", error);
      showToast(error.message, 5000, ToastStatusEnum.ERROR);
    }
  };

  useEffect(() => {
    fetchDisputes();
  }, [fetchDisputes]);

  const handleReviewClick = (dispute: Dispute) => {
    setSelectedDispute(dispute);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedDispute(null);
  };

  const statusOptions = [
    { value: "all", label: "All statuses" },
    { value: "Review", label: "Review" },
    { value: "Funds processed", label: "Funds processed" },
    { value: "Rejected", label: "Rejected" },
  ];

  const dateOptions = [
    { value: "all", label: "All Time" },
    { value: "7", label: "Last 7 days" },
    { value: "30", label: "Last 30 days" },
    { value: "90", label: "Last 90 days" },
  ];

  const getIcon = (title: string) => {
    if (title.toLowerCase().includes("camera")) {
      return <CameraIcon className="text-gray-500 w-6 h-6" />;
    }
    if (title.toLowerCase().includes("headphone")) {
      return <HeadphonesIcon className="text-gray-500 w-6 h-6" />;
    }
    if (title.toLowerCase().includes("laptop")) {
      return <LaptopIcon className="text-gray-500 w-6 h-6" />;
    }
    if (title.toLowerCase().includes("phone")) {
      return <SmartphoneIcon className="text-gray-500 w-6 h-6" />;
    }
    return <ImageIcon className="text-gray-500 w-6 h-6" />;
  };

  return (
    <AdminWrapper>
      <div className="p-6 bg-gray-50 min-h-screen">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-[#1E293B]">
              Disputes and refunds
            </h1>
            <p className="text-gray-600 mt-1">
              Manage customer disputes and process refunds
            </p>
          </div>
        </div>

        <div>
          <div className=" bg-white p-4 rounded-lg shadow-sm mb-6">
            <div className="flex items-end gap-4">
              <div className="flex-[2]">
                <label
                  htmlFor="search"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Search
                </label>
                <MkdInputV2
                  value={uiFilters.search}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    handleFilterInputChange("search", e.target.value)
                  }
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Field
                      placeholder="Search listings"
                      className="md:col-span-1 w-full h-10 px-3 !border !border-[#D1D5DB] !rounded-md !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59] focus:outline-none focus:ring-2 focus:border-transparent"
                    />
                  </MkdInputV2.Container>
                </MkdInputV2>
              </div>

              <div className="flex-[1]">
                <label
                  htmlFor="status"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Status
                </label>
                <MkdInputV2
                  type="mapping"
                  mapping={statusOptions.reduce(
                    (acc, curr) => ({ ...acc, [curr.value]: curr.label }),
                    {}
                  )}
                  value={uiFilters.status}
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                    handleFilterInputChange("status", e.target.value)
                  }
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Field className="md:col-span-1 w-full h-10 px-3 !border !border-[#D1D5DB] !rounded-md !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59] focus:outline-none focus:ring-2 focus:border-transparent" />
                  </MkdInputV2.Container>
                </MkdInputV2>
              </div>
              <div className="flex-[1]">
                <label
                  htmlFor="date"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Date Range
                </label>
                <MkdInputV2
                  type="mapping"
                  mapping={dateOptions.reduce(
                    (acc, curr) => ({ ...acc, [curr.value]: curr.label }),
                    {}
                  )}
                  value={uiFilters.date}
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                    handleFilterInputChange("date", e.target.value)
                  }
                >
                  <MkdInputV2.Container>
                    <MkdInputV2.Field
                      placeholder="Search listings"
                      className="md:col-span-1 w-full h-10 px-3 !border !border-[#D1D5DB] !rounded-md !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59] focus:outline-none focus:ring-2 focus:border-transparent"
                    />
                  </MkdInputV2.Container>
                </MkdInputV2>
              </div>
              <div className="h-full">
                <InteractiveButton
                  onClick={handleApplyFilters}
                  className="!bg-[#0F2C59] !text-white !h-full !px-4 !py-4 !rounded-md !font-medium hover:!bg-[#1a3a6b] transition-colors"
                >
                  Apply filters
                </InteractiveButton>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          {loading ? (
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <p className="text-gray-500">Loading...</p>
            </div>
          ) : disputes.length > 0 ? (
            disputes.map((dispute, index) => (
              <div
                key={index}
                className="bg-white p-6 rounded-lg shadow-sm flex items-center justify-between"
              >
                <div className="flex items-center">
                  <div className="bg-gray-100 p-3 rounded-lg mr-4 w-12 h-12 flex items-center justify-center">
                    {dispute.icon || getIcon(dispute.title)}
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg text-gray-900 mb-1">
                      {dispute.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-1">
                      {dispute.requester}
                      {dispute.reason ? ` - ${dispute.reason}` : ""}
                    </p>
                    <p className="text-gray-500 text-xs mb-2">
                      Submitted: {dispute.date}
                    </p>
                    <p className="text-[#E63946] font-bold text-base">
                      Amount: eBa$ {dispute.amount}
                    </p>
                  </div>
                </div>
                <InteractiveButton
                  onClick={() =>
                    dispute.status === "Review" && handleReviewClick(dispute)
                  }
                  className={`${
                    dispute.status === "Review"
                      ? "!bg-[#0F2C59] !text-white !px-4 !py-2 !rounded-md !font-medium hover:!bg-[#1a3a6b] !min-w-[100px]"
                      : dispute.status === "Funds processed"
                        ? "!bg-gray-300 !text-gray-700 !px-4 !py-2 !rounded-md !font-medium !cursor-not-allowed !min-w-[100px]"
                        : "!bg-gray-300 !text-gray-700 !px-4 !py-2 !rounded-md !font-medium !cursor-not-allowed !min-w-[100px]"
                  }`}
                  disabled={dispute.status !== "Review"}
                >
                  {dispute.status}
                </InteractiveButton>
              </div>
            ))
          ) : (
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex flex-col items-center justify-center py-10">
                <div className="mb-4 ">
                  <img
                    src={emptyPageImg}
                    alt="No records"
                    className="w-full h-full"
                  />
                </div>
                <div className="text-gray-500 text-lg font-medium">
                  No records found.
                </div>
              </div>
            </div>
          )}
        </div>
        <div className="mt-6">
          {pagination && pagination.total > 0 && (
            <PaginationBar
              currentPage={apiParams.page}
              pageCount={pagination.num_pages}
              pageSize={apiParams.limit}
              canPreviousPage={pagination.page > 1}
              canNextPage={pagination.page < pagination.num_pages}
              updatePageSize={handleLimitChange}
              updateCurrentPage={handlePageChange}
              startSize={50}
              multiplier={100}
              canChangeLimit={true}
            />
          )}
        </div>
      </div>
      {selectedDispute && (
        <Modal
          isOpen={isModalOpen}
          modalCloseClick={handleCloseModal}
          title="Review dispute request"
          modalHeader={true}
          classes={{
            modalDialog: "!w-full max-w-2xl bg-white",
            modalHeader: "bg-white",
            modalTitle: "!text-black",
          }}
        >
          <div className="p-6">
            <div className="bg-gray-50 p-4 rounded-lg flex justify-between items-start">
              <div>
                <h4 className="text-lg font-semibold">
                  {selectedDispute.title}
                </h4>
                <p className="text-sm text-gray-500">
                  Disputed by {selectedDispute.requester}
                </p>
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-[#E63946]">
                  eBa$ {selectedDispute.amount}
                </p>
                <p className="text-sm text-gray-500">Disputed amount</p>
              </div>
            </div>

            <div className="mt-6">
              <h5 className="font-semibold text-lg mb-4">Product Images</h5>
              <div className="grid grid-cols-2 gap-6">
                {selectedDispute.images?.map((image: string, index: number) => (
                  <div key={index} className="text-center">
                    <div className="w-full h-48 bg-gray-100 rounded-xl flex items-center justify-center border border-gray-200 p-2">
                      <img
                        src={image}
                        alt={`dispute-img-${index}`}
                        className="max-w-full max-h-full object-contain"
                      />
                    </div>
                    <p className="text-sm text-gray-600 mt-3">
                      Image {index + 1}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            <div className="mt-8">
              <h5 className="font-semibold text-lg mb-4">
                Dispute Description
              </h5>
              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <p className="text-sm text-gray-600 leading-relaxed">
                  {selectedDispute.description}
                </p>
              </div>
            </div>
          </div>
          <div className="flex justify-center p-6 bg-white border-t border-gray-200 rounded-b-lg gap-3">
            <InteractiveButton
              onClick={() =>
                handleStatusUpdate(selectedDispute.id, "Funds processed")
              }
              className="!bg-[#1DB954] text-white flex items-center font-semibold text-sm py-2 px-5 rounded-lg"
            >
              <CheckIcon className="mr-1.5 w-4 h-4" />
              Approve refund
            </InteractiveButton>
            <InteractiveButton
              onClick={() => handleStatusUpdate(selectedDispute.id, "Rejected")}
              className="!bg-[#D93025] text-white flex items-center font-semibold text-sm py-2 px-5 rounded-lg"
            >
              <XIcon className="mr-1.5 w-4 h-4" />
              Reject
            </InteractiveButton>
          </div>
        </Modal>
      )}
    </AdminWrapper>
  );
};

export default AdminDisputesAndRefundsListPage;
