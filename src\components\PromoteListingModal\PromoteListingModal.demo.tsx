import React, { useState } from "react";
import PromoteListingModal from "./PromoteListingModal";

const PromoteListingModalDemo: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleConfirmPromotion = async (days: number) => {
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      console.log(`Promoting listing for ${days} days`);
      alert(`Listing promoted for ${days} days successfully!`);
      setIsLoading(false);
      setIsModalOpen(false);
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">
          Promote Listing Modal Demo
        </h1>
        <p className="text-gray-600 mb-6">
          Click the button below to see the promote listing modal in action.
        </p>

        <button
          onClick={handleOpenModal}
          className="w-full bg-[#0F2C59] text-white px-6 py-3 rounded-md hover:bg-[#0a1f3d] transition-colors font-medium"
        >
          Open Promote Modal
        </button>

        <div className="mt-6 p-4 bg-gray-50 rounded-md">
          <h3 className="font-semibold text-gray-700 mb-2">Features:</h3>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Pixel-perfect design matching the provided image</li>
            <li>• Number input validation</li>
            <li>• Loading states</li>
            <li>• Proper color scheme (#0F2C59 blue)</li>
            <li>• Responsive design</li>
          </ul>
        </div>
      </div>

      <PromoteListingModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onConfirm={handleConfirmPromotion}
      />
    </div>
  );
};

export default PromoteListingModalDemo;
