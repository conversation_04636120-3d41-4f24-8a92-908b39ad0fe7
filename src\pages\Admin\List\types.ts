export interface IApplication {
  id: number;
  user: string;
  emergencyContact: string;
  references: string;
  status: "Active" | "Pending" | "Rejected";
  rating: string;
  documentsStatus: "Verified" | "Pending" | "Rejected";
}

export interface IReference {
  id: number;
  full_name: string;
  relationship: string;
  phone_number: string;
  email: string;
}

export interface IDocument {
  id: number;
  document_type: string;
  url: string;
  status: "Verified" | "Pending" | "Rejected";
}
