import React from "react";
import { useNavigate } from "react-router-dom";
import { AdminWrapper } from "../../../components/AdminWrapper";
import { ChevronLeftIcon } from "@heroicons/react/24/solid";
import { useListingDetails } from "@/hooks/useListing";
import { MkdLoader } from "@/components/MkdLoader";
import { useContexts } from "@/hooks/useContexts";
import { useEffect } from "react";
import emptyPageImg from "@/assets/images/empty-page.png";

const AdminViewListingPage = () => {
  const navigate = useNavigate();
  const { listingDetails, loading, error } = useListingDetails();
  const { globalDispatch } = useContexts();

  useEffect(() => {
    if (error) {
      globalDispatch({
        type: "SNACKBAR",
        payload: {
          message: error.message,
          toastStatus: "error",
        },
      });
    }
  }, [error, globalDispatch]);

  if (loading) {
    return (
      <AdminWrapper>
        <div className="flex justify-center items-center h-screen">
          <MkdLoader />
        </div>
      </AdminWrapper>
    );
  }

  if (error) {
    return (
      <AdminWrapper>
        <div className="text-center p-6">
          <p>Could not load listing details.</p>
        </div>
      </AdminWrapper>
    );
  }

  if (!listingDetails) {
    return (
      <AdminWrapper>
        <div className="flex flex-col items-center justify-center py-20">
          <div className="mb-4 ">
            <img
              src={emptyPageImg}
              alt="No records"
              className="w-full h-full"
            />
          </div>
          <div className="text-gray-500 text-lg font-medium">
            No records found.
          </div>
        </div>
      </AdminWrapper>
    );
  }

  return (
    <AdminWrapper>
      <div className="p-6 bg-[#F8F9FB] min-h-screen">
        <div className="flex justify-between items-center mb-4">
          <div>
            <button
              onClick={() => navigate(-1)}
              className="flex items-center text-gray-500 hover:text-gray-700"
            >
              <ChevronLeftIcon className="h-5 w-5 mr-2" />
              Back to Listings
            </button>
            <h1 className="text-3xl font-bold text-gray-800 mt-2">
              View Listing Details
            </h1>
            <p className="text-gray-500">
              Moderate and manage marketplace listings
            </p>
          </div>
          <div className="flex items-center">
            <span
              className={`px-3 py-1 text-xs font-semibold rounded-full mr-4 ${
                listingDetails.status === "Active"
                  ? "text-green-700 bg-green-100"
                  : "text-yellow-700 bg-yellow-100"
              }`}
            >
              {listingDetails.status}
            </span>
            <img
              className="h-10 w-10 rounded-full"
              src={
                listingDetails.seller.photo ||
                "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
              }
              alt="User profile"
            />
          </div>
        </div>

        {/* Basic Information */}
        <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
          <h2 className="text-xl font-bold text-gray-800 mb-4">
            Basic Information
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <p className="text-sm text-gray-500">Title</p>
              <p className="font-semibold text-gray-800">
                {listingDetails.title}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Category</p>
              <p className="font-semibold text-gray-800">
                {listingDetails.category}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Price</p>
              <p className="font-semibold text-[#E63946] text-xl">
                {listingDetails.price}
              </p>
            </div>
          </div>
        </div>

        {/* Description */}
        <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
          <h2 className="text-xl font-bold text-gray-800 mb-4">Description</h2>
          <p className="text-gray-600">{listingDetails.description}</p>
        </div>

        {/* Product Images */}
        <div className="bg-white p-6 rounded-lg shadow-sm mb-6">
          <h2 className="text-xl font-bold text-gray-800 mb-4">
            Product Images
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {listingDetails.images.map((image, index) => (
              <div
                key={index}
                className="bg-gray-100 rounded-lg aspect-square flex items-center justify-center overflow-hidden"
              >
                <img
                  src={image}
                  alt={`Product image ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </AdminWrapper>
  );
};

export default AdminViewListingPage;
