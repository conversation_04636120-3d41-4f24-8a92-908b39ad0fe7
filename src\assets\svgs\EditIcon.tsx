import React from "react";

const EditIcon = ({ className = "", onClick = () => {} }) => {
  return (
    <svg
      className={`${className}`}
      onClick={onClick}
      width="15"
      height="15"
      viewBox="0 0 15 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_6_682)">
        <g clip-path="url(#clip1_6_682)">
          <path
            d="M13.8172 1.34335C13.2184 0.744519 12.2504 0.744519 11.6516 1.34335L10.8285 2.16366L13.5055 4.84061L14.3285 4.01757C14.9273 3.41874 14.9273 2.45077 14.3285 1.85194L13.8172 1.34335ZM5.63594 7.35897C5.46914 7.52577 5.34062 7.73085 5.2668 7.9578L4.45742 10.3859C4.37812 10.6211 4.44102 10.8808 4.61602 11.0586C4.79102 11.2363 5.05078 11.2965 5.28867 11.2172L7.7168 10.4078C7.94102 10.334 8.14609 10.2055 8.31562 10.0387L12.8902 5.46132L10.2105 2.78163L5.63594 7.35897ZM3.54687 2.49999C2.09766 2.49999 0.921875 3.67577 0.921875 5.12499V12.125C0.921875 13.5742 2.09766 14.75 3.54687 14.75H10.5469C11.9961 14.75 13.1719 13.5742 13.1719 12.125V9.49999C13.1719 9.016 12.7809 8.62499 12.2969 8.62499C11.8129 8.62499 11.4219 9.016 11.4219 9.49999V12.125C11.4219 12.609 11.0309 13 10.5469 13H3.54687C3.06289 13 2.67187 12.609 2.67187 12.125V5.12499C2.67187 4.641 3.06289 4.24999 3.54687 4.24999H6.17187C6.65586 4.24999 7.04687 3.85897 7.04687 3.37499C7.04687 2.891 6.65586 2.49999 6.17187 2.49999H3.54687Z"
            fill="#2563EB"
          />
        </g>
      </g>
      <defs>
        <clipPath id="clip0_6_682">
          <rect
            width="14"
            height="14"
            fill="white"
            transform="translate(0.921875 0.75)"
          />
        </clipPath>
        <clipPath id="clip1_6_682">
          <path d="M0.921875 0.75H14.9219V14.75H0.921875V0.75Z" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default EditIcon;
