# Admin Pages

This file lists all the admin pages in the project.

## View Pages

- `AdminDeliveryAgentComplaintDetailsPage.tsx`: Displays details of a delivery agent complaint.
  - `AdminViewTransactionPage.tsx`: Shows the details of a specific transaction.
  <!-- - `AdminViewListingPage.tsx`: Displays the details of a single listing. -->
  <!-- - `ViewAdminWireframeTablePage.tsx`: A page for viewing a wireframe table. -->
  <!-- - `LandingPage.tsx`: The admin landing page. -->

## List Pages

- `AdminDeliveryAgentComplaintsListPage.tsx`: Lists all complaints filed against delivery agents.
- `AdminReportedListingListPage.tsx`: Lists all reported listings.
<!-- - `AdminPlatformSettingsListPage.tsx`: Lists all platform settings. -->
- `AdminPromotionsAndSponsorshipsListPage.tsx`: Lists all promotions and sponsorships.
<!-- - `AdminCategoriesListPage.tsx`: Lists all categories. -->
<!-- - `AdminRewardsAndReferralsListPage.tsx`: Lists all rewards and referrals. -->
- `AdminDeliveryApplicationsListPage.tsx`: Lists all delivery agent applications.
- `AdminDisputesAndRefundsListPage.tsx`: Lists all disputes and refunds.
- `AdminTransactionsListPage.tsx`: Lists all transactions.
- `AdminTopUpRequestsListPage.tsx`: Lists all top-up requests.
<!-- - `AdminListingsListPage.tsx`: Lists all listings. -->
<!-- - `AdminUsersListPage.tsx`: Lists all users. -->
<!-- - `ListAdminWireframeTablePage.tsx`: A page for listing a wireframe table. -->

## Add Pages

- `AdminAddCategoryPage.tsx`: Page to add a new category.
- `AdminAddUserPage.tsx`: Page for adding a new user.
<!-- - `AddAdminWireframeTablePage.tsx`: A page for adding to a wireframe table. -->

## Dashboard Pages

<!-- - `AdminDashboardPage.tsx`: The main admin dashboard. -->

## Auth Pages

<!-- - `AdminLoginPage.tsx`: Admin login page. -->

- `AdminSignUpPage.tsx`: Admin sign-up page.
- `AdminResetPage.tsx`: Admin password reset page.
- `AdminProfilePage.tsx`: Admin profile page.
- `AdminForgotPage.tsx`: Admin forgot password page.

## Edit Pages

<!-- - `EditWireframePage.tsx`: Page for editing a wireframe. -->

## Modals with API Integration

The following modals are used across the admin pages and require API integration:

- `ActionConfirmationModal`: Used for confirming actions like republishing, hiding, or deleting listings.
  - Found in: `AdminReportedListingListPage.tsx`, `AdminPromotionsAndSponsorshipsListPage.tsx`, `AdminCategoriesListPage.tsx`, `AdminDeliveryApplicationsListPage.tsx`

- `DeliveryVerifyDocumentsModal`: Used for verifying delivery agent documents.
  - Found in: `AdminDeliveryApplicationsListPage.tsx`, `DeliveryVerifyDocumentsModal.tsx`

- `ReferencesModal`: Used for viewing delivery agent references.
  - Found in: `AdminDeliveryApplicationsListPage.tsx`, `ReferencesModal.tsx`

- `Modal` (unnamed): A generic modal component is used for handling disputes and refunds.
  - Found in: `AdminDisputesAndRefundsListPage.tsx`

- A modal is suggested for rejecting top-up requests to collect an `admin_remark`.
  - Found in: `AdminTopUpRequestsListPage.tsx`

- `ManagePermissionsModal`: Used for managing user permissions.
  - Found in: `AdminUsersListPage.tsx`

- `VerifyDocumentsModal`: Used for verifying user documents.
  - Found in: `AdminUsersListPage.tsx`



- `UpdatePasswordModal`: Used for updating user passwords.
  - Found in: `UserProfile.tsx` (which is used by `AdminProfilePage.tsx`)


- A modal is implied for suspending a user, likely requiring a confirmation step.
  - Found in: `AdminDeliveryAgentComplaintDetailsPage.tsx`
