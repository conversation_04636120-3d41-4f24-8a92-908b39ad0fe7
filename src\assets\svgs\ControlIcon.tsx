import React from "react";

const ControlIcon = ({ className = "", fill = "#A8A8A8", onClick = () => { } }) => {
  return (
    <svg
      className={`${className}`}
      onClick={onClick}
      xmlns="http://www.w3.org/2000/svg"
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
    >
      <path
        d="M7.125 1.5C6.50368 1.5 6 2.00368 6 2.625V4.8295C6 5.12787 6.11853 5.41402 6.3295 5.625L8.33709 7.63258C8.7032 7.9987 9.2968 7.9987 9.66291 7.63258L11.6705 5.625C11.8815 5.41402 12 5.12787 12 4.8295V2.625C12 2.00368 11.4963 1.5 10.875 1.5H7.125Z"
        fill={fill}
      />
      <path
        d="M16.5 7.125C16.5 6.50368 15.9963 6 15.375 6H13.1705C12.8721 6 12.586 6.11853 12.375 6.3295L10.3674 8.33709C10.0013 8.7032 10.0013 9.2968 10.3674 9.66291L12.375 11.6705C12.586 11.8815 12.8721 12 13.1705 12H15.375C15.9963 12 16.5 11.4963 16.5 10.875V7.125Z"
        fill={fill}
      />
      <path
        d="M10.875 16.5C11.4963 16.5 12 15.9963 12 15.375V13.1705C12 12.8721 11.8815 12.586 11.6705 12.375L9.66291 10.3674C9.2968 10.0013 8.7032 10.0013 8.33709 10.3674L6.3295 12.375C6.11853 12.586 6 12.8721 6 13.1705V15.375C6 15.9963 6.50368 16.5 7.125 16.5H10.875Z"
        fill={fill}
      />
      <path
        d="M1.5 10.875C1.5 11.4963 2.00368 12 2.625 12H4.8295C5.12787 12 5.41402 11.8815 5.625 11.6705L7.63258 9.66291C7.9987 9.2968 7.9987 8.7032 7.63258 8.33709L5.625 6.3295C5.41402 6.11853 5.12787 6 4.8295 6H2.625C2.00368 6 1.5 6.50368 1.5 7.125V10.875Z"
        fill={fill}
      />
    </svg>
  );
};

export default ControlIcon;
