export default function SpiralArrow() {
  return (
    <svg
      width="154"
      height="85"
      viewBox="0 0 154 85"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M138.868 41.5597C138.818 41.1242 139.131 40.7308 139.566 40.6811C141.86 40.4193 143.77 39.7583 146.016 38.9293C146.014 38.93 146.013 38.9303 146.013 38.9303C146.013 38.9303 146.022 38.9266 146.049 38.9156L146.164 38.8683C146.259 38.8295 146.387 38.7779 146.532 38.7209C146.821 38.6084 147.195 38.4692 147.544 38.3659C147.717 38.3145 147.899 38.267 148.07 38.2376C148.213 38.213 148.455 38.1799 148.692 38.2357C148.819 38.2656 149.036 38.3419 149.206 38.5508C149.396 38.7835 149.424 39.0518 149.393 39.2541C149.366 39.4345 149.291 39.5796 149.234 39.6742C149.172 39.7762 149.097 39.8726 149.02 39.9616C147.94 41.2008 146.751 42.3321 145.576 43.4287C145.432 43.5636 145.288 43.6979 145.144 43.8319C144.11 44.7946 143.098 45.737 142.153 46.7373C142.042 46.8546 141.512 47.4721 141.01 48.0778C140.762 48.3762 140.532 48.6592 140.373 48.863C140.318 48.9338 140.276 48.9891 140.247 49.0291C140.152 49.2517 139.956 49.428 139.702 49.489C139.63 49.5065 139.37 49.5599 139.099 49.3946C138.781 49.2009 138.723 48.886 138.718 48.7445C138.712 48.6091 138.743 48.5054 138.755 48.4687C138.77 48.4203 138.787 48.3821 138.798 48.3595C138.834 48.2821 138.88 48.2137 138.902 48.1823C138.958 48.1002 139.036 47.996 139.121 47.8873C139.295 47.6642 139.538 47.3664 139.788 47.0645C140.281 46.4697 140.846 45.8091 140.999 45.647C141.982 44.6071 143.034 43.6278 144.064 42.6686C144.208 42.5349 144.351 42.4015 144.493 42.2685C145.122 41.6815 145.74 41.0984 146.335 40.5033C144.171 41.2986 142.161 41.9824 139.746 42.2581C139.311 42.3079 138.917 41.9951 138.868 41.5597ZM140.205 49.0887C140.205 49.0888 140.206 49.0874 140.208 49.0845C140.206 49.0872 140.205 49.0887 140.205 49.0887Z"
        fill="#FB923C"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M77.544 15.7511C79.4803 16.8338 81.0922 18.967 81.7172 20.9967C83.6143 27.1572 81.6117 34.4452 79.8541 39.991C78.4581 44.3957 77.0357 48.081 74.948 51.7954C76.6985 54.8586 78.8715 57.4532 81.4423 59.4399C85.8551 62.8501 91.4945 64.5131 98.3314 63.6293C112.316 61.8214 123.88 54.121 135.453 46.4139C137.449 45.0846 139.446 43.755 141.455 42.4554C141.823 42.2174 142.314 42.3228 142.552 42.6908C142.79 43.0589 142.685 43.5502 142.317 43.7882C140.345 45.0638 138.373 46.3783 136.39 47.6997C124.832 55.4035 112.92 63.3439 98.5349 65.2034C91.3041 66.1381 85.237 64.3784 80.4718 60.6958C77.9387 58.7383 75.789 56.2498 74.0291 53.361C73.5234 54.1874 72.9808 55.0204 72.3943 55.868C66.0452 65.0446 56.6459 69.496 45.5489 67.4494C34.2694 65.3691 23.4955 59.6863 13.8697 53.9274C9.80786 51.4974 6.27754 49.0366 3.15772 45.3788C2.87328 45.0453 2.91304 44.5444 3.24653 44.26C3.58001 43.9756 4.08093 44.0153 4.36536 44.3488C7.32035 47.8133 10.6794 50.1692 14.6846 52.5653C24.2872 58.3103 34.8547 63.8631 45.8368 65.8885C56.2593 67.8106 65.0591 63.6802 71.089 54.9649C71.8362 53.885 72.5101 52.8299 73.1261 51.7805C71.9959 49.6698 71.0558 47.3758 70.3078 44.9426C69.1472 41.1672 68.0091 36.0316 67.827 30.9504C67.6457 25.8939 68.4058 20.7483 71.1911 17.0891C72.0889 15.9096 73.1579 15.2516 74.3109 15.0729C75.4486 14.8966 76.5636 15.2029 77.544 15.7511ZM74.0557 50.1204C73.1841 48.3633 72.4392 46.4744 71.825 44.4762C70.6917 40.79 69.589 35.7964 69.4132 30.8935C69.2366 25.9659 70.0016 21.2725 72.4541 18.0505C73.1476 17.1393 73.8738 16.7468 74.554 16.6414C75.2497 16.5336 76.006 16.7096 76.7693 17.1364C78.3204 18.0037 79.6852 19.7913 80.2003 21.4638C81.9309 27.0839 80.1176 33.9056 78.341 39.5115C77.0818 43.4847 75.814 46.8238 74.0557 50.1204Z"
        fill="#FB923C"
      />
    </svg>
  );
}
