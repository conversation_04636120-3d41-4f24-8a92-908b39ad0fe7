import React, { use<PERSON><PERSON>back, useEffect, useState, Suspense } from "react";
import { useF<PERSON>, SubmitHandler } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { AdminWrapper } from "../../../components/AdminWrapper";
import { InteractiveButton } from "../../../components/InteractiveButton";
import { MkdInputV2 } from "../../../components/MkdInputV2";
import { useSDK } from "../../../hooks/useSDK";
import { Skeleton } from "../../../components/Skeleton";
import { PaginationBar } from "../../../components/PaginationBar";
import { ActionConfirmationModal } from "../../../components/ActionConfirmationModal";
import { useToast } from "../../../components/Toast";

interface Category {
  id: number;
  name: string;
  listings_count: number;
  status: string;
}

interface CategorySuggestion {
  name: string;
  suggested_count: number;
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  num_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

interface FormValues {
  search: string;
}

const AdminCategoriesListPage = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>();
  const navigate = useNavigate();
  const { sdk } = useSDK();
  const { success, error: showError, clearAllToasts } = useToast();

  const [categories, setCategories] = useState<Category[]>([]);
  const [suggestions, setSuggestions] = useState<CategorySuggestion[]>([]);
  const [pagination, setPagination] = useState<Pagination | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [confirmModal, setConfirmModal] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    onConfirm: () => void;
  }>({ isOpen: false, title: "", message: "", onConfirm: () => {} });

  const [apiParams, setApiParams] = useState({
    search: String(""),
    page: Number(1),
    limit: Number(10),
  });

  const fetchCategories = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/admin/categories`,
        method: "GET",
        params: apiParams,
      });

      console.log("Categories API Response:", response);

      if (!response.error) {
        // Ensure data is an array and properly formatted
        const categoriesData = Array.isArray(response.data)
          ? response.data
          : [];
        const processedCategories = categoriesData.map((category: any) => ({
          id: Number(category.id),
          name: String(category.name || ""),
          listings_count: Number(category.listings_count || 0),
          status: String(category.status || "inactive"),
        }));

        setCategories(processedCategories);

        // Handle pagination safely
        const paginationData = (response as any).pagination || {};
        setPagination({
          page: Number(paginationData.page || 1),
          limit: Number(paginationData.limit || 10),
          total: Number(paginationData.total || 0),
          num_pages: Number(paginationData.num_pages || 1),
          has_next: Boolean(paginationData.has_next),
          has_prev: Boolean(paginationData.has_prev),
        });
      } else {
        setError(String(response.message || "Failed to fetch categories."));
      }
    } catch (error: any) {
      console.error("Error fetching categories:", error);
      setError(String(error?.message || "An unexpected error occurred."));
    } finally {
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [apiParams]);

  const fetchSuggestions = useCallback(async () => {
    try {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/admin/category_suggestions`,
        method: "GET",
      });

      console.log("Suggestions API Response:", response);

      if (!response.error) {
        // Ensure data is an array and properly formatted
        const suggestionsData = Array.isArray(response.data)
          ? response.data
          : [];
        const processedSuggestions = suggestionsData.map((suggestion: any) => ({
          name: String(suggestion.name || ""),
          suggested_count: Number(suggestion.suggested_count || 0),
        }));

        setSuggestions(processedSuggestions);
      }
    } catch (error) {
      console.error("Error fetching suggestions:", error);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    fetchCategories();
    fetchSuggestions();
  }, [fetchCategories, fetchSuggestions]);

  const onSearchSubmit: SubmitHandler<FormValues> = (data) => {
    console.log("Form data:", data);
    const searchValue = String(data?.search || "");
    setApiParams((prev) => ({ ...prev, search: searchValue, page: 1 }));
  };

  const handleAddCategory = () => {
    navigate("/admin/categories/add");
  };

  const handleEdit = (id: number) => {
    navigate(`/admin/categories/edit/${id}`);
  };

  const handleDelete = async (id: number) => {
    try {
      clearAllToasts();
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/admin/categories/${id}`,
        method: "DELETE",
      });
      if (!response.error) {
        success(response.message || "Category action completed.");
        fetchCategories();
      } else {
        showError(response.message || "Failed to complete action.");
      }
    } catch (err: any) {
      showError(err?.message || "An error occurred.");
    } finally {
      setConfirmModal({
        isOpen: false,
        title: "",
        message: "",
        onConfirm: () => {},
      });
    }
  };

  const handleApprove = async (name: string) => {
    try {
      clearAllToasts();
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/admin/category_suggestions/approve`,
        method: "POST",
        body: { name },
      });
      if (!response.error) {
        success(response.message || "Suggestion approved.");
        fetchCategories();
        fetchSuggestions();
      } else {
        showError(response.message || "Failed to approve.");
      }
    } catch (err: any) {
      showError(err?.message || "An error occurred.");
    } finally {
      setConfirmModal({
        isOpen: false,
        title: "",
        message: "",
        onConfirm: () => {},
      });
    }
  };

  const handleReject = async (name: string) => {
    try {
      clearAllToasts();
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/admin/category_suggestions/reject`,
        method: "POST",
        body: { name },
      });
      if (!response.error) {
        success(response.message || "Suggestion rejected.");
        fetchSuggestions();
      } else {
        showError(response.message || "Failed to reject.");
      }
    } catch (err: any) {
      showError(err?.message || "An error occurred.");
    } finally {
      setConfirmModal({
        isOpen: false,
        title: "",
        message: "",
        onConfirm: () => {},
      });
    }
  };

  const confirmAction = (
    action: () => void,
    title: string,
    message: string
  ) => {
    setConfirmModal({
      isOpen: true,
      title,
      message,
      onConfirm: action,
    });
  };

  const handlePageChange = (newPage: number) => {
    setApiParams((prev) => ({ ...prev, page: newPage }));
  };

  const handleLimitChange = (newLimit: number) => {
    setApiParams((prev) => ({ ...prev, limit: newLimit, page: 1 }));
  };

  // Debug: Log confirmModal state before rendering modal
  console.log("confirmModal", confirmModal);
  return (
    <AdminWrapper>
      <div className="p-6 bg-[#F8F9FB] min-h-full">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-[#1E293B]">Categories</h1>
            <p className="text-sm text-gray-500 mt-1">
              Manage marketplace categories
            </p>
          </div>
          <div className="flex items-center">
            <InteractiveButton
              onClick={handleAddCategory}
              className="!bg-[#1E293B] !hover:bg-gray-900 !text-white !font-semibold !px-4 !py-2 !rounded-md !flex !items-center !text-sm"
            >
              + Add category
            </InteractiveButton>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4">
          <form
            onSubmit={handleSubmit(onSearchSubmit)}
            className="flex items-center"
          >
            <div className="relative flex-grow">
              <MkdInputV2
                register={register}
                errors={errors}
                name="search"
                placeholder="Search categories"
                className="w-full"
              >
                <MkdInputV2.Field className="!py-2.5 !px-4 !border !border-gray-300 !rounded-md" />
              </MkdInputV2>
            </div>
            <InteractiveButton
              type="submit"
              className="!bg-[#1F2937] !hover:bg-gray-900 !text-white !font-semibold !px-6 !py-2.5 !rounded-md !ml-4 !whitespace-nowrap !text-sm"
            >
              Search
            </InteractiveButton>
          </form>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md mb-6 mt-6">
          <h2 className="text-lg font-bold mb-4 text-[#111827]">
            Existing Categories
          </h2>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-xs uppercase tracking-wider">
                    Name
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-xs uppercase tracking-wider">
                    Listings
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-xs uppercase tracking-wider">
                    Status
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-xs uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {loading ? (
                  Array.from({ length: 5 }).map((_, index) => (
                    <tr key={index}>
                      <td colSpan={4} className="py-4 px-4">
                        <Skeleton className="h-4 w-full" />
                      </td>
                    </tr>
                  ))
                ) : error ? (
                  <tr>
                    <td colSpan={4} className="text-center py-10 text-red-500">
                      {error}
                    </td>
                  </tr>
                ) : categories.length > 0 ? (
                  categories.map((category) => (
                    <tr key={category.id}>
                      <td className="py-4 px-4 text-sm text-gray-800">
                        {String(category.name || "")}
                      </td>
                      <td className="py-4 px-4 text-sm text-gray-500">
                        {String(category.listings_count || 0)}
                      </td>
                      <td className="py-4 px-4">
                        <span
                          className={`${String(category.status) === "active" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"} text-xs font-semibold px-2.5 py-1 rounded-full`}
                        >
                          {String(category.status || "inactive")}
                        </span>
                      </td>
                      <td className="py-4 px-4 text-sm">
                        <button
                          onClick={() => handleEdit(Number(category.id))}
                          className="text-blue-600 hover:underline font-medium mr-4"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() =>
                            confirmAction(
                              () => handleDelete(Number(category.id)),
                              "Confirm Delete",
                              "Are you sure you want to delete this category? If it has listings, it will be marked as inactive instead."
                            )
                          }
                          className="text-red-600 hover:underline font-medium"
                        >
                          Delete
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={4} className="text-center py-10">
                      No categories found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          {pagination && pagination.total > 0 && (
            <PaginationBar
              currentPage={pagination.page}
              pageCount={pagination.num_pages}
              pageSize={pagination.limit}
              canPreviousPage={pagination.has_prev}
              canNextPage={pagination.has_next}
              updatePageSize={handleLimitChange}
              updateCurrentPage={handlePageChange}
              startSize={5}
              multiplier={5}
              canChangeLimit={true}
            />
          )}
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-lg font-bold mb-4 text-[#111827]">
            New Category Suggestions
          </h2>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-xs uppercase tracking-wider">
                    Suggested Category Name
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-xs uppercase tracking-wider">
                    Number of People Suggested
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-gray-600 text-xs uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {loading ? (
                  Array.from({ length: 3 }).map((_, index) => (
                    <tr key={index}>
                      <td colSpan={3} className="py-4 px-4">
                        <Skeleton className="h-4 w-full" />
                      </td>
                    </tr>
                  ))
                ) : suggestions.length > 0 ? (
                  suggestions.map((suggestion) => (
                    <tr key={String(suggestion.name)}>
                      <td className="py-4 px-4 text-sm text-gray-800">
                        {String(suggestion.name || "")}
                      </td>
                      <td className="py-4 px-4 text-sm text-gray-500">
                        {String(suggestion.suggested_count || 0)}
                      </td>
                      <td className="py-4 px-4 text-sm">
                        <button
                          onClick={() =>
                            confirmAction(
                              () => handleApprove(String(suggestion.name)),
                              "Confirm Approval",
                              "Are you sure you want to approve this category?"
                            )
                          }
                          className="text-green-600 hover:underline font-medium mr-4"
                        >
                          Approve
                        </button>
                        <button
                          onClick={() =>
                            confirmAction(
                              () => handleReject(String(suggestion.name)),
                              "Confirm Rejection",
                              "Are you sure you want to reject this category suggestion?"
                            )
                          }
                          className="text-red-600 hover:underline font-medium"
                        >
                          Reject
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={3} className="text-center py-10">
                      No new suggestions.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <Suspense fallback={null}>
        <ActionConfirmationModal
          isOpen={Boolean(confirmModal.isOpen)}
          onClose={() => setConfirmModal({ ...confirmModal, isOpen: false })}
          onSuccess={
            typeof confirmModal.onConfirm === "function"
              ? confirmModal.onConfirm
              : () => {}
          }
          title={String(confirmModal.title || "")}
          customMessage={String(confirmModal.message || "")}
        />
      </Suspense>
    </AdminWrapper>
  );
};

export default AdminCategoriesListPage;
