import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import MkdSDK from "../utils/MkdSDK";

const sdk = new MkdSDK();

const PLATFORM_SETTINGS_QUERY_KEY = ["platformSettings"];

export const useGetPlatformSettings = () => {
  return useQuery({
    queryKey: PLATFORM_SETTINGS_QUERY_KEY,
    queryFn: () =>
      sdk.request({
        endpoint: "/v2/api/ebadollar/custom/admin/platform-settings",
        method: "GET",
      }),
  });
};

export const useUpdatePlatformSettings = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: any) =>
      sdk.request({
        endpoint: "/v2/api/ebadollar/custom/admin/platform-settings",
        method: "PUT",
        body: data,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: PLATFORM_SETTINGS_QUERY_KEY });
    },
  });
};
