import { useSDK } from "@/hooks/useSDK";
import { useQuery } from "@tanstack/react-query";

export const useAdminDashboard = () => {
  const { sdk } = useSDK();

  const fetchDashboardData = async () => {
    const response = await sdk.callRawAPI(
      "/v1/api/ebadollar/admin/dashboard/summary",
      {},
      "GET"
    );
    return response.data;
  };

  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["adminDashboard"],
    queryFn: fetchDashboardData,
  });

  return {
    data,
    isLoading,
    isError,
    error,
  };
};
