import React from "react";

const CheckIcon = ({ className }: { className?: string }) => (
  <>
    <svg
      width="15"
      height="14"
      viewBox="0 0 15 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.6929 0.394753C15.0272 0.63933 15.0999 1.1086 14.8553 1.4429L6.07592 13.4429C5.95542 13.6076 5.77301 13.7161 5.57076 13.7433C5.36852 13.7706 5.1639 13.7142 5.00411 13.5873L0.283519 9.83731C-0.040813 9.57966 -0.0948719 9.10787 0.162775 8.78354C0.420421 8.45921 0.892208 8.40515 1.21654 8.6628L5.3261 11.9274L13.6447 0.557204C13.8893 0.222907 14.3586 0.150175 14.6929 0.394753Z"
        fill="currentColor"
      />
    </svg>
  </>
);

export default CheckIcon;
