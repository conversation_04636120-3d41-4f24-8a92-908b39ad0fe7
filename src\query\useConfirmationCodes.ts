import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useSDK } from "../hooks/useSDK";

// Types
export interface IConfirmationCodeData {
  transactionId: number;
  listingName: string;
  buyerName?: string;
  sellerName?: string;
  pickupCode?: string;
  deliveryCode?: string;
  generatedAt: string;
  expiresAt: string;
  isExpired: boolean;
  isVerified: boolean;
  verifiedAt?: string;
  pickupVerified?: boolean;
  deliveryAgent?: {
    id: number;
    name: string;
    assignmentStatus: string;
  } | null;
  instructions: string;
}

export interface ICodeVerificationRequest {
  assignmentId: number;
  pickupCode?: string;
  deliveryCode?: string;
}

export interface ICodeVerificationResponse {
  error: boolean;
  message: string;
  data: {
    assignmentId: number;
    status: string;
    verifiedAt: string;
  };
}

// Pickup Code Query (for sellers)
export const usePickupCodeQuery = (transactionId: number) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["pickup-code", transactionId],
    queryFn: async (): Promise<IConfirmationCodeData> => {
      console.log("🚀 Fetching pickup code for transaction:", transactionId);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/transactions/${transactionId}/pickup-code`,
          method: "GET",
        });
        console.log("📡 Pickup Code API Response:", response);

        if (response.error) {
          throw new Error(response.message || "Failed to fetch pickup code");
        }

        return response.data;
      } catch (error) {
        console.error("❌ Pickup Code API Call Error:", error);
        throw error;
      }
    },
    enabled: !!transactionId && transactionId > 0,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 60 * 1000, // Refetch every minute to check verification status
  });
};

// Delivery Code Query (for buyers)
export const useDeliveryCodeQuery = (transactionId: number) => {
  const { sdk } = useSDK();

  return useQuery({
    queryKey: ["delivery-code", transactionId],
    queryFn: async (): Promise<IConfirmationCodeData> => {
      console.log("🚀 Fetching delivery code for transaction:", transactionId);

      try {
        const response = await sdk.request({
          endpoint: `/v2/api/ebadollar/custom/member/transactions/${transactionId}/delivery-code`,
          method: "GET",
        });
        console.log("📡 Delivery Code API Response:", response);

        if (response.error) {
          throw new Error(response.message || "Failed to fetch delivery code");
        }

        return response.data;
      } catch (error) {
        console.error("❌ Delivery Code API Call Error:", error);
        throw error;
      }
    },
    enabled: !!transactionId && transactionId > 0,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 60 * 1000, // Refetch every minute to check verification status
  });
};

// Verify Pickup Code Mutation (for delivery agents)
export const useVerifyPickupCodeMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: ICodeVerificationRequest) => {
      console.log("🚀 Verifying pickup code:", data);

      try {
        const response = await sdk.request({
          endpoint:
            "/v2/api/ebadollar/custom/member/delivery-agent/verify-pickup-code",
          method: "POST",
          body: {
            assignmentId: data.assignmentId,
            pickupCode: data.pickupCode,
          },
        });
        console.log("📡 Verify Pickup Code API Response:", response);

        if (response.error) {
          throw new Error(response.message || "Failed to verify pickup code");
        }

        return response;
      } catch (error) {
        console.error("❌ Verify Pickup Code API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log("✅ Pickup code verified successfully:", data);
      // Invalidate related queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["pickup-code"] });
      queryClient.invalidateQueries({ queryKey: ["delivery-assignments"] });
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
    },
    onError: (error) => {
      console.error("❌ Failed to verify pickup code:", error);
    },
  });
};

// Verify Delivery Code Mutation (for delivery agents)
export const useVerifyDeliveryCodeMutation = () => {
  const { sdk } = useSDK();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: ICodeVerificationRequest) => {
      console.log("🚀 Verifying delivery code:", data);

      try {
        const response = await sdk.request({
          endpoint:
            "/v2/api/ebadollar/custom/member/delivery-agent/verify-delivery-code",
          method: "POST",
          body: {
            assignmentId: data.assignmentId,
            deliveryCode: data.deliveryCode,
          },
        });
        console.log("📡 Verify Delivery Code API Response:", response);

        if (response.error) {
          throw new Error(response.message || "Failed to verify delivery code");
        }

        return response;
      } catch (error) {
        console.error("❌ Verify Delivery Code API Call Error:", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log("✅ Delivery code verified successfully:", data);
      // Invalidate related queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["delivery-code"] });
      queryClient.invalidateQueries({ queryKey: ["delivery-assignments"] });
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
    },
    onError: (error) => {
      console.error("❌ Failed to verify delivery code:", error);
    },
  });
};

// Helper functions
export const formatCodeExpiration = (expiresAt: string): string => {
  const expirationDate = new Date(expiresAt);
  const now = new Date();
  const diffMs = expirationDate.getTime() - now.getTime();

  if (diffMs <= 0) {
    return "Expired";
  }

  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

  if (diffHours > 0) {
    return `Expires in ${diffHours}h ${diffMinutes}m`;
  } else {
    return `Expires in ${diffMinutes}m`;
  }
};

export const getCodeStatusColor = (
  isExpired: boolean,
  isVerified: boolean
): string => {
  if (isVerified) return "text-green-600";
  if (isExpired) return "text-red-600";
  return "text-orange-600";
};

export const getCodeStatusText = (
  isExpired: boolean,
  isVerified: boolean
): string => {
  if (isVerified) return "Verified";
  if (isExpired) return "Expired";
  return "Active";
};
