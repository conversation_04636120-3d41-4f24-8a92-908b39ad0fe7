import React from "react";
import { InteractiveButton } from "@/components/InteractiveButton";

interface QuickActionCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  buttonText: string;
  onButtonClick: () => void;
}

const QuickActionCard = ({
  icon,
  title,
  description,
  buttonText,
  onButtonClick,
}: QuickActionCardProps) => {
  return (
    <div className="flex flex-col items-center justify-between rounded-lg bg-white p-6 text-center shadow-md">
      <div className="mb-4">{icon}</div>
      <h3 className="mb-2 text-lg font-bold text-gray-800">{title}</h3>
      <p className="mb-4 text-sm text-gray-600">{description}</p>
      <InteractiveButton
        onClick={onButtonClick}
        className="w-full bg-red-500 py-2 text-white hover:bg-red-600"
      >
        {buttonText}
      </InteractiveButton>
    </div>
  );
};

export default QuickActionCard;
