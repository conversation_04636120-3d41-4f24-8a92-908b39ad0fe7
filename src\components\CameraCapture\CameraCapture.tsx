import React, { useRef, useState, useCallback } from "react";
import Webcam from "react-webcam";
import InteractiveButton from "../InteractiveButton/InteractiveButton";

interface CameraCaptureProps {
  onCapture: (imageData: string) => void;
  onClose: () => void;
}

const CameraCapture: React.FC<CameraCaptureProps> = ({
  onCapture,
  onClose,
}) => {
  const webcamRef = useRef<Webcam>(null);
  const [isCameraReady, setIsCameraReady] = useState(false);

  const handleCapture = useCallback(() => {
    const imageSrc = webcamRef.current?.getScreenshot();
    if (imageSrc) {
      onCapture(imageSrc);
    }
  }, [onCapture]);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-lg w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Take a Selfie</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            ✕
          </button>
        </div>

        <div className="relative">
          <Webcam
            ref={webcamRef}
            audio={false}
            screenshotFormat="image/jpeg"
            onUserMedia={() => setIsCameraReady(true)}
            videoConstraints={{
              facingMode: "user",
              width: { ideal: 1280 },
              height: { ideal: 720 },
            }}
            className="w-full rounded-lg"
          />
          {!isCameraReady && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
              <div className="text-gray-500">Loading camera...</div>
            </div>
          )}
        </div>

        <div className="flex justify-end gap-3 mt-4">
          <InteractiveButton
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 !text-gray-700 bg-white hover:bg-gray-50 rounded-md"
          >
            Cancel
          </InteractiveButton>
          <InteractiveButton
            type="button"
            onClick={handleCapture}
            disabled={!isCameraReady}
            className="px-4 py-2 bg-[#0F2C59] hover:bg-[#0F2C59]/90 !text-white rounded-md"
          >
            Take Photo
          </InteractiveButton>
        </div>
      </div>
    </div>
  );
};

export default CameraCapture;
