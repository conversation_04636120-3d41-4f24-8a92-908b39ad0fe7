import React from "react";
import { AdminWrapper } from "../../../components/AdminWrapper";
import DashboardStatCard from "../../../components/DashboardStatCard";
import { MkdSimpleTable } from "../../../components/MkdSimpleTable";
import {
  UsersIcon,
  ListingsIcon,
  DisputesAndRefundsIcon,
} from "@/assets/svgs/AdminDashboard";
import { useAdminDashboard } from "@/hooks/useAdminDashboard";
import { Skeleton } from "@/components/Skeleton";
import { format } from "date-fns";

const AdminDashboardPage = () => {
  const { data, isLoading } = useAdminDashboard();

  const recentTransactionsColumns = [
    {
      header: "",
      accessor: "transaction",
    },
    {
      header: "",
      accessor: "amount",
    },
  ];

  const recentApplicationsColumns = [
    {
      header: "",
      accessor: "applicant",
    },
    {
      header: "",
      accessor: "status",
    },
  ];

  const recentTransactionsData =
    data?.recent_transactions.map((item: any) => {
      const userData = JSON.parse(item.user_data || "{}");
      return {
        transaction: (
          <div className="flex items-center py-2">
            <img
              src={userData.photo || "https://i.pravatar.cc/40"}
              alt="User"
              className="h-10 w-10 rounded-full"
            />
            <div className="ml-3">
              <p className="font-semibold text-gray-900">
                {userData.name || "John Doe"}
              </p>
              <p className="text-sm text-gray-500">
                Purchase #{item.id || "12345"}
              </p>
            </div>
          </div>
        ),
        amount: (
          <div className="text-right">
            <p className="font-semibold text-red-500">
              eBa$ {item.amount || "209.99"}
            </p>
          </div>
        ),
      };
    }) || [];

  const recentApplicationsData =
    data?.recent_applications.map((item: any) => {
      const userData = JSON.parse(item.user_data || "{}");
      return {
        applicant: (
          <div className="flex items-center py-2">
            <img
              src={userData.photo || "https://i.pravatar.cc/40"}
              alt="User"
              className="h-10 w-10 rounded-full"
            />
            <div className="ml-3">
              <p className="font-semibold text-gray-900">
                {userData.name || "John Doe"}
              </p>
              <p className="text-sm text-gray-500">Delivery Agent</p>
            </div>
          </div>
        ),
        status: (
          <div className="text-right">
            <span className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-800">
              {item.status || "Pending"}
            </span>
          </div>
        ),
      };
    }) || [];

  return (
    <AdminWrapper>
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {isLoading ? (
            <>
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
            </>
          ) : (
            <>
              <DashboardStatCard
                title="Total Users"
                value={data?.stats?.total_users || 0}
                icon={<UsersIcon className="text-blue-500" />}
                type="balance"
              />
              <DashboardStatCard
                title="Active Listings"
                value={data?.stats?.active_listings || 0}
                icon={<ListingsIcon className="text-green-500" />}
                type="balance"
              />
              <DashboardStatCard
                title="Pending Disputes"
                value={data?.stats?.pending_disputes || 0}
                icon={<DisputesAndRefundsIcon className="text-red-500" />}
                type="balance"
              />
            </>
          )}
        </div>

        <div className="mt-8 flex justify-between w-full gap-6">
          <div className="rounded-lg bg-white p-6 shadow w-full">
            <h3 className="text-lg font-semibold text-gray-700">
              Revenue Breakdown
            </h3>
            {isLoading ? (
              <Skeleton className="mt-4 h-48 w-full" />
            ) : (
              <ul className="mt-4 space-y-3">
                <li className="flex justify-between">
                  <span className="text-gray-600">USD Fees</span>
                  <span className="font-bold text-[#1E293B]">
                    USD${" "}
                    {(
                      Number(data?.revenue?.usd_fees) || 0
                    ).toLocaleString()}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600">eBa$ Fees</span>
                  <span className="font-semibold text-red-500">
                    eBa${" "}
                    {(
                      Number(data?.revenue?.ebas_fees) || 0
                    ).toLocaleString()}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600">Transaction Fees</span>
                  <span className="font-semibold text-[#1E293B]">
                    USD${" "}
                    {(
                      Number(data?.revenue?.transaction_fees) || 0
                    ).toLocaleString()}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600">Credit Balance Fees</span>
                  <span className="font-semibold text-[#1E293B]">
                    USD${" "}
                    {(
                      Number(data?.revenue?.credit_balance_fees) || 0
                    ).toLocaleString()}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600">FX Transaction Fees</span>
                  <span className="font-semibold text-[#1E293B]">
                    USD${" "}
                    {(
                      Number(data?.revenue?.fx_transaction_fees) || 0
                    ).toLocaleString()}
                  </span>
                </li>
              </ul>
            )}
          </div>
          <div className="rounded-lg bg-white p-6 shadow w-full">
            <h3 className="text-lg font-semibold text-gray-700">Expenses</h3>
            {isLoading ? (
              <Skeleton className="mt-4 h-48 w-full" />
            ) : (
              <ul className="mt-4 space-y-3">
                <li className="flex justify-between">
                  <span className="text-gray-600">Loyalty Rewards Paid</span>
                  <span className="font-semibold text-red-500">
                    eBa${" "}
                    {(
                      Number(data?.expenses?.loyalty_rewards_paid) || 0
                    ).toLocaleString()}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600">Referral Commissions</span>
                  <span className="font-semibold text-red-500">
                    eBa${" "}
                    {(
                      Number(data?.expenses?.referral_commissions) || 0
                    ).toLocaleString()}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600">Signup Fees Collected</span>
                  <span className="font-semibold text-red-500">
                    eBa${" "}
                    {(
                      Number(data?.expenses?.signup_fees_collected) || 0
                    ).toLocaleString()}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600">Commission Expenses</span>
                  <span className="font-semibold text-red-500">
                    eBa${" "}
                    {(
                      Number(data?.expenses?.commission_expenses) || 0
                    ).toLocaleString()}
                  </span>
                </li>
              </ul>
            )}
          </div>
          <div className="rounded-lg bg-white p-6 shadow w-full">
            <h3 className="text-lg font-semibold text-gray-700">
              Credit Line Metrics
            </h3>
            {isLoading ? (
              <Skeleton className="mt-4 h-48 w-full" />
            ) : (
              <ul className="mt-4 space-y-3">
                <li className="flex justify-between">
                  <span className="text-gray-600">
                    Total Credit Line Issued
                  </span>
                  <span className="font-semibold text-red-500">
                    eBa${" "}
                    {(
                      Number(data?.credit_line?.total_credit_line_issued) ||
                      0
                    ).toLocaleString()}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600">Credit Line Used</span>
                  <span className="font-semibold text-red-500">
                    eBa${" "}
                    {(
                      Number(data?.credit_line?.credit_line_used) || 0
                    ).toLocaleString()}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600">Outstanding Balances</span>
                  <span className="font-semibold text-red-500">
                    eBa${" "}
                    {(
                      Number(data?.credit_line?.outstanding_balances) || 0
                    ).toLocaleString()}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600">Overdue Credit Balances</span>
                  <span className="font-semibold text-red-500">
                    eBa${" "}
                    {(
                      Number(data?.credit_line?.overdue_credit_balances) || 0
                    ).toLocaleString()}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600">Utilization Rate</span>
                  <span className="font-semibold text-green-600">
                    {Number(data?.credit_line?.utilization_rate) || 62.8}%
                  </span>
                </li>
              </ul>
            )}
          </div>
        </div>

        {/* <div className="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
          <div className="rounded-lg bg-white p-6 shadow">
            <h3 className="text-lg font-semibold">Profitability Analysis</h3>
            {isLoading ? (
              <Skeleton className="mt-4 h-48 w-full" />
            ) : (
              <ul className="mt-4 space-y-2">
                <li className="flex justify-between">
                  <span>Gross Profit (eBa$)</span>
                  <span className="font-semibold text-green-600">
                    eBa${" "}
                    {(
                      Number(data?.profitability?.gross_profit_eba) || 0
                    ).toFixed(2)}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span>Gross Profit (USD)</span>
                  <span className="font-semibold text-green-600">
                    $
                    {(
                      Number(data?.profitability?.gross_profit_usd) || 0
                    ).toFixed(2)}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span>Net Profit (eBa$)</span>
                  <span className="font-semibold text-green-600">
                    eBa${" "}
                    {(Number(data?.profitability?.net_profit_eba) || 0).toFixed(
                      2
                    )}
                  </span>
                </li>
                <li className="flex justify-between border-t pt-2">
                  <span className="font-semibold">Profit Margin</span>
                  <span className="font-bold text-green-600">
                    {Number(data?.profitability?.profit_margin) || 0}%
                  </span>
                </li>
              </ul>
            )}
          </div>
          <div className="rounded-lg bg-white p-6 shadow">
            <h3 className="text-lg font-semibold">Platform Health</h3>
            {isLoading ? (
              <Skeleton className="mt-4 h-48 w-full" />
            ) : (
              <ul className="mt-4 space-y-2">
                <li className="flex justify-between">
                  <span>Credit Utilization</span>
                  <span
                    className={`font-semibold ${
                      (Number(data?.credit_line?.utilization_rate) || 0) > 80
                        ? "text-red-600"
                        : (Number(data?.credit_line?.utilization_rate) || 0) >
                            60
                          ? "text-orange-600"
                          : "text-green-600"
                    }`}
                  >
                    {Number(data?.credit_line?.utilization_rate) || 0}%
                  </span>
                </li>
                <li className="flex justify-between">
                  <span>Overdue Users</span>
                  <span className="font-semibold text-red-600">
                    {Number(data?.credit_line?.overdue_users) || 0}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span>Active Listings</span>
                  <span className="font-semibold text-blue-600">
                    {Number(data?.stats?.active_listings) || 0}
                  </span>
                </li>
                <li className="flex justify-between">
                  <span>Pending Disputes</span>
                  <span
                    className={`font-semibold ${
                      (Number(data?.stats?.pending_disputes) || 0) > 10
                        ? "text-red-600"
                        : (Number(data?.stats?.pending_disputes) || 0) > 5
                          ? "text-orange-600"
                          : "text-green-600"
                    }`}
                  >
                    {Number(data?.stats?.pending_disputes) || 0}
                  </span>
                </li>
              </ul>
            )}
          </div>
        </div> */}

        <div className="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
          <div className="rounded-lg bg-white p-6 shadow">
            <h3 className="text-lg font-semibold">Recent Transactions</h3>
            {isLoading ? (
              <Skeleton className="mt-4 h-48 w-full" />
            ) : (
              <MkdSimpleTable
                columns={recentTransactionsColumns}
                data={recentTransactionsData}
                className="mt-4"
              />
            )}
          </div>
          <div className="rounded-lg bg-white p-6 shadow">
            <h3 className="text-lg font-semibold">Recent Applications</h3>
            {isLoading ? (
              <Skeleton className="mt-4 h-48 w-full" />
            ) : (
              <MkdSimpleTable
                columns={recentApplicationsColumns}
                data={recentApplicationsData}
                className="mt-4"
              />
            )}
          </div>
        </div>
      </div>
    </AdminWrapper>
  );
};

export default AdminDashboardPage;
