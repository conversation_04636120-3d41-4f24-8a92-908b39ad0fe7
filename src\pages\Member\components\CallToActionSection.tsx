import React from "react";
import { useNavigate } from "react-router-dom";

const CallToActionSection = () => {
  const navigate = useNavigate();

  const handleCreateAccount = () => {
    navigate("/signup");
  };

  return (
    <section 
      className="py-20"
      style={{
        background: "linear-gradient(135deg, #0D3166 0%, #1E40AF 100%)"
      }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        {/* Main Heading */}
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-8">
          Ready to Trade Without Boundaries?
        </h2>

        {/* CTA Button */}
        <button
          onClick={handleCreateAccount}
          className="bg-white text-sm font-medium px-8 py-3 rounded-md hover:bg-gray-50 transition-colors duration-200"
          style={{ color: "#0D3166" }}
        >
          Create Your Free Account Now
        </button>
      </div>
    </section>
  );
};

export default CallToActionSection;
