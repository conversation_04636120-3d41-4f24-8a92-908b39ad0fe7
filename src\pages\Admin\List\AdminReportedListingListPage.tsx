import React, { useEffect, useState } from "react";
import { AdminWrapper } from "../../../components/AdminWrapper";
import { MkdInputV2 } from "../../../components/MkdInputV2";
import { InteractiveButton } from "../../../components/InteractiveButton";
import SearchIcon from "../../../assets/svgs/SearchIcon";
import { useSDK } from "../../../hooks/useSDK";
import { MkdLoader } from "../../../components/MkdLoader";
import { PaginationBar } from "../../../components/PaginationBar";
import { ActionConfirmationModal } from "../../../components/ActionConfirmationModal";
import { useAdminListingActions } from "@/hooks/useAdminListingActions";
import { useToast } from "@/hooks/useToast";
import { ToastStatusEnum } from "@/utils/Enums";
import emptyPageImg from "@/assets/images/empty-page.png";

interface IListing {
  id: string;
  title: string;
  seller: string;
  reports: number;
  reasons: string[];
  status: string;
  report_id: number;
}

const getReportPillColor = (reports: number) => {
  if (reports > 20) return "bg-red-100 text-red-800";
  if (reports > 10) return "bg-yellow-100 text-yellow-800";
  return "bg-green-100 text-green-800";
};

const AdminReportedListingListPage = () => {
  const [listings, setListings] = useState<IListing[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<{
    page: number;
    limit: number;
    total: number;
    num_pages: number;
  } | null>(null);
  const [uiFilters, setUiFilters] = useState({
    search: "",
    status: "",
    sort: "reports_count,desc",
  });
  const [apiParams, setApiParams] = useState({
    page: 1,
    limit: 10,
    sort_by: "reports_count",
    sort_order: "desc",
    status: "",
    search: "",
  });
  const [actionModal, setActionModal] = useState<{
    open: boolean;
    listingId: string | null;
    action: string | null;
    reportId: number | null;
  }>({ open: false, listingId: null, action: null, reportId: null });

  const { sdk } = useSDK();
  const { showToast } = useToast();
  const {
    updateListingStatus,
    deleteListing,
    isLoading: isActionLoading,
  } = useAdminListingActions();

  const fetchListings = async () => {
    setLoading(true);
    try {
      const result = await sdk.callRawAPI(
        "/v2/api/ebadollar/custom/admin/reported-listings",
        apiParams,
        "GET"
      );
      if (!result.error) {
        console.log("Reported listings data:", result.data);
        setListings(result.data);
        setPagination(result.pagination);
      }
    } catch (error) {
      console.error("Error fetching listings", error);
      showToast("Error fetching listings", 5000, ToastStatusEnum.ERROR);
    } finally {
      setLoading(false);
    }
  };

  const handleAction = async () => {
    if (!actionModal.listingId || !actionModal.action) return;
    const listingId = parseInt(actionModal.listingId.replace("#", ""), 10);
    const reportId = actionModal.reportId ?? undefined;

    try {
      let result;
      switch (actionModal.action) {
        case "Republish":
          result = await updateListingStatus({
            listingId,
            status: "active",
            reportId,
          });
          break;
        case "Hide":
          result = await updateListingStatus({
            listingId,
            status: "hidden",
            reportId,
          });
          break;
        case "Delete":
          result = await deleteListing({ listingId, reportId });
          break;
        default:
          return;
      }
      if (!result.error) {
        showToast(
          `Listing ${String(actionModal.action).toLowerCase()}ed successfully`,
          5000,
          ToastStatusEnum.SUCCESS
        );
        fetchListings();
      } else {
        showToast(
          result.message || "An error occurred.",
          5000,
          ToastStatusEnum.ERROR
        );
      }
    } catch (error: any) {
      console.error(
        `Error ${String(actionModal.action).toLowerCase()}ing listing`,
        error
      );
      showToast(
        error.message ||
          `Error ${String(actionModal.action).toLowerCase()}ing listing`,
        5000,
        ToastStatusEnum.ERROR
      );
    } finally {
      setActionModal({
        open: false,
        listingId: null,
        action: null,
        reportId: null,
      });
    }
  };

  useEffect(() => {
    fetchListings();
  }, [apiParams]);

  const handleFilterInputChange = (key: string, value: string | number) => {
    setUiFilters((prev) => ({ ...prev, [key]: value }));
  };

  const handleApplyFilters = () => {
    const [sort_by, sort_order] = uiFilters.sort.split(",");
    setApiParams({
      ...apiParams,
      search: uiFilters.search,
      status: uiFilters.status,
      sort_by,
      sort_order,
      page: 1,
    });
  };

  const handlePageChange = (newPage: number) => {
    setApiParams((prev) => ({ ...prev, page: newPage }));
  };

  const handleLimitChange = (newLimit: number) => {
    setApiParams((prev) => ({ ...prev, limit: newLimit, page: 1 }));
  };

  const statusOptions = [
    { value: "", label: "All Reports" },
    { value: "pending", label: "Pending" },
    { value: "hidden", label: "Hidden" },
    { value: "active", label: "Republished" },
  ];

  const sortOptions = [
    { value: "reports_count,desc", label: "Sort by: Reports" },
    { value: "created_at,desc", label: "Sort by: Newest" },
  ];

  return (
    <AdminWrapper>
      <ActionConfirmationModal
        isOpen={actionModal.open}
        onClose={() =>
          setActionModal({
            open: false,
            listingId: null,
            action: null,
            reportId: null,
          })
        }
        onSuccess={handleAction}
        title={`Confirm ${actionModal.action}`}
        customMessage={`Are you sure you want to ${actionModal.action?.toLowerCase()} this listing?`}
        isLoading={isActionLoading}
      />
      <div className="p-6 bg-[#F8F9FB] min-h-screen">
        <div>
          <h1 className="text-3xl font-bold text-[#1E293B]">
            Reported Listings
          </h1>
          <p className="text-sm text-gray-500 mt-1">
            Review and moderate reported marketplace listings
          </p>
        </div>

        <div className="mt-6 bg-white p-4 rounded-lg shadow-sm">
          <div className="flex items-end space-x-4">
            <div className="flex-1">
              <label
                htmlFor="search"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Search
              </label>
              <MkdInputV2
                value={uiFilters.search}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  handleFilterInputChange("search", e.target.value)
                }
                placeholder="Search listings..."
              >
                <MkdInputV2.Container className="relative">
                  <MkdInputV2.Field className="!border !border-[#D1D5DB] pl-10 !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59]" />
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <SearchIcon className="h-5 w-5 text-gray-400" />
                  </div>
                </MkdInputV2.Container>
              </MkdInputV2>
            </div>
            <div className="w-48">
              <label
                htmlFor="status"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Status
              </label>
              <MkdInputV2
                type="mapping"
                mapping={statusOptions.reduce(
                  (acc, curr) => ({ ...acc, [curr.value]: curr.label }),
                  {}
                )}
                value={uiFilters.status}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                  handleFilterInputChange("status", e.target.value);
                }}
              >
                <MkdInputV2.Container>
                  <MkdInputV2.Field className="!border !border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59]" />
                </MkdInputV2.Container>
              </MkdInputV2>
            </div>
            <div className="w-48">
              <label
                htmlFor="sort"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Sort by
              </label>
              <MkdInputV2
                type="mapping"
                mapping={sortOptions.reduce(
                  (acc, curr) => ({ ...acc, [curr.value]: curr.label }),
                  {}
                )}
                value={uiFilters.sort}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                  handleFilterInputChange("sort", e.target.value);
                }}
              >
                <MkdInputV2.Container>
                  <MkdInputV2.Field className="!border !border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59]" />
                </MkdInputV2.Container>
              </MkdInputV2>
            </div>
            <div>
              <InteractiveButton
                className="!bg-[#1E293B] text-white px-6 h-10"
                onClick={handleApplyFilters}
              >
                Apply filters
              </InteractiveButton>
            </div>
          </div>
        </div>

        <div className="mt-6 bg-white rounded-lg shadow-sm">
          {loading ? (
            <MkdLoader />
          ) : (
            <>
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="p-4 text-left w-12">
                      <input
                        type="checkbox"
                        className="h-4 w-4 !border-[#D1D5DB] !focus:ring-[#0F2C59]"
                      />
                    </th>
                    <th className="p-4 text-left font-semibold text-sm text-gray-600">
                      Listing ID
                    </th>
                    <th className="p-4 text-left font-semibold text-sm text-gray-600">
                      Title
                    </th>
                    <th className="p-4 text-left font-semibold text-sm text-gray-600">
                      Seller
                    </th>
                    <th className="p-4 text-left font-semibold text-sm text-gray-600">
                      Reason
                    </th>
                    <th className="p-4 text-left font-semibold text-sm text-gray-600">
                      Reports
                    </th>
                    <th className="p-4 text-left font-semibold text-sm text-gray-600">
                      Details
                    </th>
                    <th className="p-4 text-left font-semibold text-sm text-gray-600">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {listings.length === 0 ? (
                    <tr>
                      <td colSpan={8}>
                        <div className="flex flex-col items-center justify-center py-10">
                          <div className="mb-4 ">
                            <img
                              src={emptyPageImg}
                              alt="No records"
                              className="w-full h-full"
                            />
                          </div>
                          <div className="text-gray-500 text-lg font-medium">
                            No reported listings found.
                          </div>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    listings.map((listing) => (
                      <tr key={listing.id} className="border-b">
                        <td className="p-4">
                          <input
                            type="checkbox"
                            className="h-4 w-4 !border-[#D1D5DB] !focus:ring-[#0F2C59]"
                          />
                        </td>
                        <td className="p-4 text-sm text-gray-700">
                          {String(listing.id || "")}
                        </td>
                        <td className="p-4 text-sm text-gray-700">
                          {String(listing.title || "")}
                        </td>
                        <td className="p-4 text-sm text-gray-700">
                          {String(listing.seller || "")}
                        </td>
                        <td className="p-4 text-sm">
                          <a href="#" className="text-blue-600 hover:underline">
                            View reasons
                          </a>
                        </td>
                        <td className="p-4 text-sm">
                          <span
                            className={`px-2 py-1 rounded-full text-xs font-medium ${getReportPillColor(
                              Number(listing.reports) || 0
                            )}`}
                          >
                            {Number(listing.reports) || 0} reports
                          </span>
                        </td>
                        <td className="p-4 text-sm">
                          <a href="#" className="text-blue-600 hover:underline">
                            View details
                          </a>
                        </td>
                        <td className="p-4 text-sm">
                          <div className="flex items-center space-x-4">
                            {listing.status !== "active" && (
                              <button
                                onClick={() =>
                                  setActionModal({
                                    open: true,
                                    listingId: listing.id,
                                    action: "Republish",
                                    reportId: listing.report_id,
                                  })
                                }
                                className="text-sm text-blue-600 hover:underline"
                              >
                                Republish
                              </button>
                            )}
                            {listing.status !== "hidden" && (
                              <button
                                onClick={() =>
                                  setActionModal({
                                    open: true,
                                    listingId: listing.id,
                                    action: "Hide",
                                    reportId: listing.report_id,
                                  })
                                }
                                className="text-sm text-yellow-600 hover:underline"
                              >
                                Hide
                              </button>
                            )}
                            <button
                              onClick={() =>
                                setActionModal({
                                  open: true,
                                  listingId: listing.id,
                                  action: "Delete",
                                  reportId: listing.report_id,
                                })
                              }
                              className="text-sm text-red-600 hover:underline"
                            >
                              Delete
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
              {pagination && (
                <PaginationBar
                  currentPage={pagination.page}
                  pageCount={pagination.num_pages}
                  pageSize={pagination.limit}
                  canPreviousPage={pagination.page > 1}
                  canNextPage={pagination.page < pagination.num_pages}
                  updatePageSize={(size) => handleLimitChange(size)}
                  updateCurrentPage={(page) => handlePageChange(page)}
                  canChangeLimit={true}
                  startSize={10}
                  multiplier={10}
                />
              )}
            </>
          )}
        </div>
      </div>
    </AdminWrapper>
  );
};

export default AdminReportedListingListPage;
