import React, { useState } from "react";
import InteractiveButton from "@/components/InteractiveButton/InteractiveButton";

interface CreateNewListingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit?: (data: any) => void;
}

const CreateNewListingModal: React.FC<CreateNewListingModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  const [listingType, setListingType] = useState("sell");
  const [amount, setAmount] = useState("");
  const [rate, setRate] = useState("1.03 USD/EBA$");
  const [paymentMethod, setPaymentMethod] = useState("Bank Transfer");
  const [bankName, setBankName] = useState("");
  const [accountHolderName, setAccountHolderName] = useState("");
  const [accountNumber, setAccountNumber] = useState("");
  const [routingCode, setRoutingCode] = useState("");
  const [additionalInstructions, setAdditionalInstructions] = useState("");

  if (!isOpen) return null;

  const handleSubmit = () => {
    const data = {
      type: listingType === "sell" ? "selling" : "buying",
      amount: parseFloat(amount),
      rate: parseFloat(rate.replace(/[^\d.]/g, "")), // Extract numeric value
      payment_method: paymentMethod,
      description: additionalInstructions,
      bank_details: {
        bank_name: bankName,
        account_holder_name: accountHolderName,
        account_number: accountNumber,
        routing_code: routingCode,
      },
    };

    if (onSubmit) {
      onSubmit(data);
    }
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-lg font-semibold text-gray-900">
              Create New Listing
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Create an offer to sell EBA$ with other users.
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-xl"
          >
            ×
          </button>
        </div>

        <div className="p-6">
          <div className="space-y-4">
            {/* Listing Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Listing Type
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="sell"
                  name="listingType"
                  value="sell"
                  checked={listingType === "sell"}
                  onChange={(e) => setListingType(e.target.value)}
                  className="w-4 h-4 text-[#0F2C59] border-gray-300 focus:ring-[#0F2C59]"
                />
                <label htmlFor="sell" className="text-sm text-gray-700">
                  Sell EBA$
                </label>
              </div>
            </div>

            {/* Amount */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Amount (EBA$)
              </label>
              <input
                type="text"
                placeholder="Enter amount"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            {/* Rate */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Rate(e.g. 1.03 USD/EBA$)
              </label>
              <input
                type="text"
                value={rate}
                onChange={(e) => setRate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
              <p className="text-xs text-gray-500 mt-1">
                Current market average: 1.02 USD/EBA$
              </p>
            </div>

            {/* Preferred Payment Method */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Preferred Payment Method
              </label>
              <select
                value={paymentMethod}
                onChange={(e) => setPaymentMethod(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm bg-white"
              >
                <option value="Bank Transfer">Bank Transfer</option>
                <option value="PayPal">PayPal</option>
                <option value="Credit Card">Credit Card</option>
                <option value="e-Transfer">e-Transfer</option>
                <option value="Local Wire">Local Wire</option>
              </select>
            </div>

            {/* Bank Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bank Name
              </label>
              <input
                type="text"
                placeholder="Enter your bank name"
                value={bankName}
                onChange={(e) => setBankName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            {/* Account Holder Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Account Holder Name
              </label>
              <input
                type="text"
                placeholder="Enter account holder name"
                value={accountHolderName}
                onChange={(e) => setAccountHolderName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            {/* Account Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Account Number
              </label>
              <input
                type="text"
                placeholder="Enter account number"
                value={accountNumber}
                onChange={(e) => setAccountNumber(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            {/* Bank Routing/Swift Code */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bank Routing/Swift Code
              </label>
              <input
                type="text"
                placeholder="Enter routing or SWIFT code"
                value={routingCode}
                onChange={(e) => setRoutingCode(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              />
            </div>

            {/* Additional Instructions */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Additional Instructions (Optional)
              </label>
              <textarea
                placeholder="Please do not try to scam me by attaching fake receipts"
                value={additionalInstructions}
                onChange={(e) => setAdditionalInstructions(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm resize-none"
              />
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 mt-6">
              <InteractiveButton
                onClick={handleCancel}
                className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 rounded-md font-medium"
              >
                Cancel
              </InteractiveButton>
              <InteractiveButton
                onClick={handleSubmit}
                className="flex-1 bg-[#0F2C59] hover:bg-[#0F2C59]/90 text-white py-2 rounded-md font-medium"
              >
                Create Listing
              </InteractiveButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateNewListingModal;
