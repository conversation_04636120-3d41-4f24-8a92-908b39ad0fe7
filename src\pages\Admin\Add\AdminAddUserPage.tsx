import React from "react";
import { useForm, Controller } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import MkdInputV2 from "../../../components/MkdInputV2";
import { InteractiveButton } from "../../../components/InteractiveButton";
import { AdminWrapper } from "../../../components/AdminWrapper";
import { MkdPasswordInput } from "../../../components/MkdPasswordInput";
import { useNavigate } from "react-router-dom";
import { useCreateUser } from "../../../query/useCreateUser";
import { useToast } from "../../../components/Toast";

const schema = yup
  .object({
    first_name: yup.string().required("First name is required"),
    last_name: yup.string().required("Last name is required"),
    email: yup.string().email("Invalid email").required("Email is required"),
    phone: yup.string().required("Phone number is required"),
    country: yup.string().required("Country is required"),
    address: yup.string().required("Address is required"),
    city: yup.string().required("City is required"),
    password: yup.string().required("Password is required"),
  })
  .required();

const AdminAddUserPage = () => {
  const navigate = useNavigate();
  const { success: showSuccess, error: showError } = useToast();
  const {
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      country: "United States",
    },
  });

  const { mutate, isPending } = useCreateUser();

  const onSubmit = (data: any) => {
    mutate(data, {
      onSuccess: () => {
        showSuccess("User created successfully");
        navigate("/admin/users");
      },
      onError: (err: any) => {
        showError(err.message);
      },
    });
  };

  return (
    <AdminWrapper>
      <div className="bg-gray-50 min-h-screen p-6  flex justify-start w-full">
        <div className=" w-full max-w-2xl ">
          <div className="bg-white rounded-lg shadow-sm p-8">
            <h1 className="text-2xl font-semibold text-gray-900 mb-8">
              Create new user
            </h1>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <Controller
                name="first_name"
                control={control}
                render={({ field }) => (
                  <MkdInputV2 {...field} errors={errors.first_name?.message}>
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>First Name</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="Loona"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                )}
              />

              <Controller
                name="last_name"
                control={control}
                render={({ field }) => (
                  <MkdInputV2 {...field} errors={errors.last_name?.message}>
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Last Name</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="Tapia"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                )}
              />

              <Controller
                name="email"
                control={control}
                render={({ field }) => (
                  <MkdInputV2 {...field} errors={errors.email?.message}>
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Email Address</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="<EMAIL>"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                )}
              />

              <Controller
                name="phone"
                control={control}
                render={({ field }) => (
                  <MkdInputV2 {...field} errors={errors.phone?.message}>
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Phone Number</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="+****************"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                )}
              />

              <Controller
                name="country"
                control={control}
                render={({ field }) => (
                  <MkdInputV2
                    type="dropdown"
                    options={["United States", "Canada", "Mexico"]}
                    {...field}
                    errors={errors.country?.message}
                  >
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Country</MkdInputV2.Label>
                      <MkdInputV2.Field className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59]" />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                )}
              />

              <Controller
                name="address"
                control={control}
                render={({ field }) => (
                  <MkdInputV2 {...field} errors={errors.address?.message}>
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>Address</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="123 Main Street, apt 4B"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                )}
              />

              <Controller
                name="city"
                control={control}
                render={({ field }) => (
                  <MkdInputV2 {...field} errors={errors.city?.message}>
                    <MkdInputV2.Container>
                      <MkdInputV2.Label>City</MkdInputV2.Label>
                      <MkdInputV2.Field
                        placeholder="New York"
                        className="!border-[#D1D5DB] !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59]"
                      />
                      <MkdInputV2.Error />
                    </MkdInputV2.Container>
                  </MkdInputV2>
                )}
              />

              <Controller
                name="password"
                control={control}
                render={({ field }) => (
                  <MkdPasswordInput
                    {...field}
                    errors={errors.password?.message}
                    label="Password"
                    placeholder="password123"
                  />
                )}
              />

              <div className="pt-4">
                <InteractiveButton
                  type="submit"
                  className="w-full !bg-[#1E293B] hover:bg-gray-900 text-white font-medium !py-3 px-4 rounded-md transition-colors duration-200"
                  disabled={isPending}
                >
                  {isPending ? "Saving..." : "Save changes"}
                </InteractiveButton>
              </div>
            </form>
          </div>
        </div>
      </div>
    </AdminWrapper>
  );
};

export default AdminAddUserPage;
