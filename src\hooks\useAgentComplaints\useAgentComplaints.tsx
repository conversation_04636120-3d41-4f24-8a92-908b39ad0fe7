import { useState, useEffect, useCallback } from "react";
import { useParams } from "react-router-dom";
import { useSDK } from "../useSDK";
import { useContexts } from "../useContexts";

interface IComplaint {
  id: number;
  status: string;
  created_at: string;
  complaint_text: string;
  agent_response: string | null;
  rating: number;
  listing: {
    id: number;
    title: string;
    image: string;
  };
  complainant: {
    id: number;
    email: string;
    name: string;
    avatar: string | null;
  };
  agent: {
    id: number;
    name: string;
    avatar: string | null;
  };
}

export const useAgentComplaints = () => {
  const { sdk } = useSDK();
  const { id: agent_id } = useParams();
  const { globalDispatch } = useContexts();

  const [complaints, setComplaints] = useState<IComplaint[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    if (!agent_id) {
      setError(new Error("Agent ID is required"));
      setLoading(false);
      return;
    }

    let isMounted = true;

    const fetchComplaints = async () => {
      if (!isMounted) return;
      
      setLoading(true);
      setError(null);
      try {
        const result = await sdk.callRawAPI(
          `/v2/api/ebadollar/custom/admin/delivery-agent-complaints/${agent_id}`,
          {},
          "GET"
        );
        if (!isMounted) return;
        
        if (!result.error) {
          setComplaints(result.data || []);
        } else {
          throw new Error(result.message || "Failed to fetch complaints");
        }
      } catch (err: any) {
        if (isMounted) {
          let errorMessage = "Failed to fetch agent complaints";
          
          if (
            err.message?.includes("ECONNRESET") ||
            err.message?.includes("connection")
          ) {
            errorMessage =
              "Database connection issue. Please try refreshing the page.";
          } else if (err.message?.includes("timeout")) {
            errorMessage = "Request timed out. Please try again.";
          } else if (err.message) {
            errorMessage = err.message;
          }
          
          setError(new Error(errorMessage));
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchComplaints();

    return () => {
      isMounted = false;
    };
  }, [agent_id]);

  const updateComplaintStatus = useCallback(async (complaintId: number, status: "valid" | "invalid") => {
    if (!agent_id) {
      globalDispatch({
        type: "SNACKBAR",
        payload: {
          message: "Agent ID is required",
          toastStatus: "error",
        },
      });
      return;
    }

    try {
      const result = await sdk.callRawAPI(
        `/v2/api/ebadollar/custom/admin/delivery-agent-complaints/complaint/${complaintId}/status`,
        { status },
        "PUT"
      );
      if (!result.error) {
        globalDispatch({
          type: "SNACKBAR",
          payload: {
            message: "Status updated successfully",
            toastStatus: "success",
          },
        });
        
        // Update the complaint in the local state
        setComplaints(prev => 
          prev.map(complaint => 
            complaint.id === complaintId 
              ? { ...complaint, status }
              : complaint
          )
        );
      } else {
        throw new Error(result.message || "Failed to update status");
      }
    } catch (err: any) {
      globalDispatch({
        type: "SNACKBAR",
        payload: {
          message: err.message,
          toastStatus: "error",
        },
      });
    }
  }, [agent_id, sdk, globalDispatch]);

  return {
    complaints,
    loading,
    error,
    updateComplaintStatus,
    agentId: agent_id
  };
};
