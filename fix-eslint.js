const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

// Function to recursively find all TypeScript/JavaScript files
function findFiles(dir, extensions = [".ts", ".tsx", ".js", ".jsx"]) {
  let results = [];
  const list = fs.readdirSync(dir);

  list.forEach((file) => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat && stat.isDirectory()) {
      // Skip node_modules and other common directories
      if (
        !["node_modules", "dist", "build", ".git", "coverage"].includes(file)
      ) {
        results = results.concat(findFiles(filePath, extensions));
      }
    } else {
      const ext = path.extname(file);
      if (extensions.includes(ext)) {
        results.push(filePath);
      }
    }
  });

  return results;
}

// Function to fix unused variables by prefixing with underscore
function fixUnusedVariables(content) {
  // Pattern to match variable declarations that might be unused
  const patterns = [
    // const/let/var declarations
    /(const|let|var)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=/g,
    // Function parameters
    /function\s*\(([^)]*)\)/g,
    // Arrow function parameters
    /\(([^)]*)\)\s*=>/g,
    // Destructuring assignments
    /(const|let|var)\s*\{([^}]*)\}\s*=/g,
    /(const|let|var)\s*\[([^\]]*)\]\s*=/g,
  ];

  let modified = content;

  // This is a simplified approach - in practice, you'd need more sophisticated parsing
  // For now, we'll focus on the most common patterns

  return modified;
}

// Function to remove console statements
function removeConsoleStatements(content) {
  // Remove console.log, console.error, console.warn, etc.
  return content.replace(
    /console\.(log|error|warn|info|debug)\s*\([^)]*\);?\s*/g,
    ""
  );
}

// Function to fix React hook dependencies
function fixReactHookDependencies(content) {
  // This is complex and would require AST parsing
  // For now, we'll leave this as a manual task
  return content;
}

// Main function to process files
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, "utf8");
    let modified = false;

    // Remove console statements
    const withoutConsole = removeConsoleStatements(content);
    if (withoutConsole !== content) {
      content = withoutConsole;
      modified = true;
    }

    if (modified) {
      fs.writeFileSync(filePath, content, "utf8");
      console.log(`Fixed: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
}

// Main execution
console.log("Starting ESLint fixes...");

// Find all TypeScript/JavaScript files
const srcDir = path.join(__dirname, "src");
const files = findFiles(srcDir);

console.log(`Found ${files.length} files to process`);

// Process each file
files.forEach(processFile);

console.log("ESLint fixes completed!");

// Run ESLint to check remaining issues
console.log("\nRunning ESLint to check remaining issues...");
try {
  execSync("npx eslint src --format=compact", { stdio: "inherit" });
} catch (error) {
  console.log("ESLint found remaining issues that need manual fixing.");
}
