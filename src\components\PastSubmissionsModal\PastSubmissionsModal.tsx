import React, { useState, useEffect } from "react";
import { Modal } from "../Modal";
import { TrashIcon, XIcon } from "../../assets/svgs";
import { User, Document } from "../../interfaces";
import { useSDK } from "../../hooks/useSDK";
import { useToast } from "../Toast";

interface PastSubmissionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
  title: string;
  applicationId?: number;
  documentType?: string;
}

export const PastSubmissionsModal: React.FC<PastSubmissionsModalProps> = ({
  isOpen,
  onClose,
  user,
  title,
  applicationId,
  documentType,
}) => {
  const { sdk } = useSDK();
  const toast = useToast();
  const [submissions, setSubmissions] = useState<Document[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen && user && documentType) {
      fetchPastSubmissions();
    }
  }, [isOpen, user, documentType, applicationId]);

  const fetchPastSubmissions = async () => {
    if (!user || !documentType) return;

    setLoading(true);
    try {
      let endpoint: string;

      // Determine which API endpoint to use based on available props
      if (applicationId) {
        // For delivery application documents
        endpoint = `/v2/api/ebadollar/custom/admin/delivery-applications/${applicationId}/documents/${documentType}/history`;
      } else {
        // For user verification documents
        endpoint = `/v2/api/ebadollar/custom/admin/users/${user.id}/documents/${documentType}/history`;
      }

      const response = await sdk.request({
        endpoint,
        method: "GET",
      });

      if (!response.error) {
        setSubmissions(response.data || []);
      } else {
        toast.error("Failed to fetch past submissions");
      }
    } catch (error) {
      console.error("Error fetching past submissions:", error);
      toast.error("Error fetching past submissions");
    } finally {
      setLoading(false);
    }
  };

  if (!user) return null;

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={onClose}
      title=""
      modalHeader={false}
      classes={{
        modal: "h-full",
        modalDialog: "w-[90%] max-w-2xl bg-white !p-6 rounded-lg",
        modalContent: "!p-0 !m-0",
      }}
    >
      <div className="flex justify-between items-center mb-4">
        <div>
          <h2 className="text-xl font-bold text-[#1E293B]">{title}</h2>
          <p className="text-sm text-gray-500">All submissions</p>
        </div>
        <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
          <XIcon />
        </button>
      </div>
      <div className="flex items-center mb-6">
        <div className="w-12 h-12 rounded-full bg-gray-300 mr-4">
          {user.photo && (
            <img
              src={user.photo}
              alt={`${user.first_name} ${user.last_name}`}
              className="w-full h-full rounded-full object-cover"
            />
          )}
        </div>
        <div>
          <h3 className="text-lg font-semibold">{`${user.first_name} ${user.last_name}`}</h3>
          <p className="text-sm text-gray-500">
            Applied on: {new Date(user.joined).toLocaleDateString()}
          </p>
        </div>
      </div>
      <div className="overflow-x-auto">
        <table className="w-full text-sm text-left">
          <thead className="text-xs text-gray-700 uppercase bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3">
                Document Details
              </th>
              <th scope="col" className="px-6 py-3 text-right">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={2} className="px-6 py-4 text-center text-gray-500">
                  Loading past submissions...
                </td>
              </tr>
            ) : submissions.length === 0 ? (
              <tr>
                <td colSpan={2} className="px-6 py-4 text-center text-gray-500">
                  No past submissions found
                </td>
              </tr>
            ) : (
              submissions.map((submission, index) => (
                <tr key={submission.id || index} className="bg-white border-b">
                  <td className="px-6 py-4">
                    <div className="space-y-2">
                      <div className="font-medium text-blue-600">
                        <a
                          href={submission.document_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="hover:underline"
                        >
                          {submission.document_url}
                        </a>
                      </div>
                      <div className="text-sm text-gray-500">
                        Submitted:{" "}
                        {new Date(submission.created_at).toLocaleString()}
                      </div>
                      <div className="text-sm">
                        Status:{" "}
                        <span
                          className={`font-medium ${
                            submission.status === "approved" ||
                            submission.status === "verified"
                              ? "text-green-600"
                              : submission.status === "rejected"
                                ? "text-red-600"
                                : "text-yellow-600"
                          }`}
                        >
                          {submission.status.charAt(0).toUpperCase() +
                            submission.status.slice(1)}
                        </span>
                      </div>
                      {submission.admin_remark && (
                        <div className="text-sm text-gray-600">
                          Remark: {submission.admin_remark}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 flex items-center justify-end gap-3">
                    <button
                      className="text-blue-500 hover:text-blue-700"
                      onClick={() =>
                        window.open(submission.document_url, "_blank")
                      }
                      title="View document"
                    >
                      <EyeIcon />
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </Modal>
  );
};

const EyeIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    className="h-5 w-5"
    fill="none"
    viewBox="0 0 24 24"
    stroke="currentColor"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
    />
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
    />
  </svg>
);
