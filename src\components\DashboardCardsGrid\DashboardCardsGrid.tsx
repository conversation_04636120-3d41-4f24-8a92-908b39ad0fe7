import React from "react";
import DashboardStatCard from "../DashboardStatCard";

export interface DashboardCardsGridProps {
  dashboardData: {
    account_balance: {
      current_balance: number;
      monthly_change: number;
    };
    credit_line: {
      limit: number;
      available: number;
      utilization_percentage: number;
      balance_due: number;
      due_date: string | null;
    };
    rating: {
      average_rating: number;
      total_ratings: number;
    };
    commissions: {
      total_amount: number;
      active_referrals: number;
    };
    rewards: {
      total_amount: number;
    };
  };
  formatCurrency: (amount: number) => string;
  formatTransactionAmount: (amount: number) => string;
  renderStars: (rating: number) => React.ReactNode;
  onPayBalance: () => void;
  paymentProcessing: boolean;
}

const DashboardCardsGrid: React.FC<DashboardCardsGridProps> = ({
  dashboardData,
  formatCurrency,
  formatTransactionAmount,
  renderStars,
  onPayBalance,
  paymentProcessing,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
      {/* eBaDollar Account Balance */}
      <DashboardStatCard
        type="balance"
        title="eBaDollar Balance"
        value={formatCurrency(dashboardData.account_balance.current_balance)}
        monthlyChange={dashboardData.account_balance.monthly_change}
        formatCurrency={formatCurrency}
        formatTransactionAmount={formatTransactionAmount}
      />

      {/* eBaCredit Line Limit */}
      <DashboardStatCard
        type="credit"
        title="eBaCredit Line Limit"
        value={formatCurrency(dashboardData.credit_line.limit)}
        subtitle={`Available Credit: ${formatCurrency(dashboardData.credit_line.available)}`}
        utilizationPercentage={dashboardData.credit_line.utilization_percentage}
        formatCurrency={formatCurrency}
      />

      {/* eBaCredit Line Balance */}
      <DashboardStatCard
        type="credit-balance"
        title="eBaCredit Line Balance"
        value={formatCurrency(dashboardData.credit_line.balance_due)}
        dueDate={dashboardData.credit_line.due_date}
        balanceDue={dashboardData.credit_line.balance_due}
        onAction={onPayBalance}
        actionText="Pay Balance"
        actionDisabled={paymentProcessing}
      />

      {/* eBa Star Rating */}
      <DashboardStatCard
        type="rating"
        title="eBa Star Rating"
        value={dashboardData.rating.average_rating.toFixed(1)}
        averageRating={dashboardData.rating.average_rating}
        totalRatings={dashboardData.rating.total_ratings}
        renderStars={renderStars}
      />

      {/* eBa Commissions */}
      <DashboardStatCard
        type="commissions"
        title="eBa Commissions"
        value={formatCurrency(dashboardData.commissions.total_amount)}
        activeReferrals={dashboardData.commissions.active_referrals}
        formatCurrency={formatCurrency}
      />

      {/* eBa Rewards */}
      <DashboardStatCard
        type="rewards"
        title="eBa Rewards"
        value={formatCurrency(dashboardData.rewards.total_amount)}
        monthlyAmount="eBa$ 45.75 this month"
        formatCurrency={formatCurrency}
      />
    </div>
  );
};

export default DashboardCardsGrid;
