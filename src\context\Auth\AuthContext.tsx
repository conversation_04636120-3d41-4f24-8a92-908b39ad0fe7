import React, { useReducer, useEffect } from "react";
import { AuthState, AuthAction, AuthContextType, UserDetails } from "./types";
import { updatedRolesFn } from "@/utils/utils";
import { useSDK } from "@/hooks/useSDK";
import { RoleEnum } from "@/utils/Enums";

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  userDetails: {
    firstName: null,
    lastName: null,
    photo: null,
  },
  token: null,
  role: null,
  sessionExpired: null,
  isLoading: false, // Start with loading false, we'll set it to true only when needed
};

export const AuthContext = React.createContext<AuthContextType>({
  ...initialState,
  dispatch: () => null,
  state: initialState,
});

const reducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case "LOGIN": {
      const {
        user_id,
        token,
        role,
        first_name,
        last_name,
        photo,
        refresh_token,
      } = action.payload;
      localStorage.setItem("user", user_id);
      localStorage.setItem("token", token);
      localStorage.setItem("role", role);

      // Store refresh token if provided
      if (refresh_token) {
        localStorage.setItem("refresh_token", refresh_token);
      }

      const userDetails: UserDetails = {
        firstName: first_name ?? null,
        lastName: last_name ?? null,
        photo: photo ?? null,
      };

      return {
        ...state,
        isAuthenticated: true,
        user: Number(user_id),
        token,
        role,
        userDetails,
        isLoading: false,
      };
    }
    case "UPDATE_PROFILE":
      return {
        ...state,
        role: localStorage.getItem("role"),
        profile: action.payload,
      };
    case "LOGOUT":
      localStorage.removeItem("user");
      localStorage.removeItem("token");
      localStorage.removeItem("role");
      localStorage.removeItem("refresh_token");
      return {
        ...initialState,
        isLoading: false,
      };
    case "SESSION_EXPIRED":
      return {
        ...state,
        sessionExpired: action.payload,
      };
    case "SET_LOADING":
      return {
        ...state,
        isLoading: action.payload,
      };
    default:
      return state;
  }
};

export const tokenExpireError = async (
  dispatch: React.Dispatch<AuthAction>,
  errorMessage: string
): Promise<void> => {
  if (errorMessage === "TOKEN_EXPIRED") {
    const role = localStorage.getItem("role") as RoleEnum;
    const refreshToken = localStorage.getItem("refresh_token");
    const userId = localStorage.getItem("user");

    // Try to refresh token first
    if (refreshToken && userId && role) {
      try {
        const sdk = new (await import("@/utils/MkdSDK")).default();
        const result = await sdk.request({
          endpoint: `/v1/api/${sdk.getProjectId()}/${role}/lambda/refresh-token`,
          method: "POST",
          body: {
            refresh_token: refreshToken,
            user_id: userId,
          },
        });

        if (!result.error) {
          // Update tokens in localStorage
          if (result.token) {
            localStorage.setItem("token", result.token);
          }
          if (result.refresh_token) {
            localStorage.setItem("refresh_token", result.refresh_token);
          }

          // Don't show session expired modal if refresh was successful
          return;
        }
      } catch (error) {
        console.error("Token refresh failed in tokenExpireError:", error);
      }
    }

    // If refresh failed or no refresh token, show session expired
    dispatch({ type: "SESSION_EXPIRED", payload: true });
  }
};

interface AuthProviderProps {
  children: React.ReactNode;
}

const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { sdk } = useSDK();

  const [state, dispatch] = useReducer(reducer, initialState);

  const refreshToken = async (role: RoleEnum) => {
    try {
      const refreshToken = localStorage.getItem("refresh_token");
      const userId = localStorage.getItem("user");

      if (!refreshToken || !userId) {
        throw new Error("No refresh token available");
      }

      const result = (await sdk.request({
        endpoint: `/v1/api/${sdk.getProjectId()}/${role}/lambda/refresh-token`,
        method: "POST",
        body: {
          refresh_token: refreshToken,
          user_id: userId,
        },
      })) as any;

      if (result.error) {
        throw new Error(result.message);
      }

      // Update tokens in localStorage
      if (result.token) {
        localStorage.setItem("token", result.token);
      }
      if (result.refresh_token) {
        localStorage.setItem("refresh_token", result.refresh_token);
      }

      return result.token;
    } catch (error) {
      console.error("Token refresh failed:", error);
      throw error;
    }
  };

  const checkToken = async (
    token: string,
    user: string,
    role: RoleEnum,
    isBackgroundCheck = false
  ) => {
    if (!isBackgroundCheck) {
      dispatch({ type: "SET_LOADING", payload: true });
    }

    try {
      const res = await sdk.getProfile();

      if (res.error) {
        throw new Error(res.message);
      }

      if (!isBackgroundCheck) {
        dispatch({
          type: "LOGIN",
          payload: {
            user_id: user,
            token,
            role,
          },
        });
      }
      // If it's a background check and token is valid, we don't need to do anything
      // as the user is already logged in
    } catch (error: any) {
      // Try to refresh token if it's expired
      if (
        error.message === "TOKEN_EXPIRED" ||
        error.message?.includes("TOKEN_EXPIRED")
      ) {
        try {
          const newToken = await refreshToken(role);
          // Retry with new token
          const retryRes = await sdk.getProfile();

          if (!retryRes.error) {
            dispatch({
              type: "LOGIN",
              payload: {
                user_id: user,
                token: newToken,
                role,
              },
            });
            return;
          }
        } catch (refreshError) {
          console.error("Token refresh failed:", refreshError);
        }
      }

      // Check if it's a network error - if so, don't logout immediately
      if (error.name === "NetworkError" || error.message?.includes("fetch")) {
        // Keep the user logged in with existing token for offline scenarios
        if (!isBackgroundCheck) {
          dispatch({
            type: "LOGIN",
            payload: {
              user_id: user,
              token,
              role,
            },
          });
        }
        return;
      }

      // For background checks, be more lenient - only logout on explicit auth errors
      if (isBackgroundCheck) {
        return;
      }

      // If refresh failed or other error, logout (only for non-background checks)
      dispatch({ type: "LOGOUT" });
      const updatedRole = updatedRolesFn(
        role as "admin" | "super_admin",
        window.location
      );

      if (updatedRole) {
        window.location.href = `/${updatedRole}/login`;
      } else {
        window.location.href = "/";
      }
    }
  };

  useEffect(() => {
    const user = localStorage.getItem("user");
    const token = localStorage.getItem("token");
    const role = localStorage.getItem("role") as RoleEnum;

    if (token && user && role) {
      // Immediately restore authentication state to prevent redirect
      dispatch({
        type: "LOGIN",
        payload: {
          user_id: user,
          token,
          role,
        },
      });

      // Then validate token in background
      checkToken(token, user, role as RoleEnum, true);
    }
  }, []);

  return (
    <AuthContext.Provider value={{ state, dispatch }}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;
