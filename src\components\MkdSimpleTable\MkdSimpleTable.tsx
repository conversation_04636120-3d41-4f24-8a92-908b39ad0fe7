interface MkdSimpleTableProps {
  columns: { header: string; accessor: string }[];
  data: any[];
  className?: string;
  title?: string;
}

const MkdSimpleTable = ({
  columns,
  data = [],
  className = "",
  title,
}: MkdSimpleTableProps) => {
  if (!data.length) {
    return (
      <div className={`w-full ${className}`}>
        {title && <h2 className="mb-4 text-xl font-bold">{title}</h2>}
        <div className="text-center py-8 text-gray-500">No data available</div>
      </div>
    );
  }

  return (
    <div className={`w-full ${className}`}>
      {title && <h2 className="mb-4 text-xl font-bold">{title}</h2>}
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <tbody className="divide-y divide-gray-200">
            {data.map((row, rowIndex) => (
              <tr key={rowIndex} className="hover:bg-gray-50">
                {columns.map((column) => (
                  <td key={column.accessor} className="px-0 py-4 text-sm">
                    {row[column.accessor]}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default MkdSimpleTable;
