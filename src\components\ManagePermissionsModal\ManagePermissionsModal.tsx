import React, { useState, useEffect } from "react";
import { Modal } from "../Modal";
import { InteractiveButton } from "../InteractiveButton";
import { User } from "../../interfaces";
import { useSDK } from "../../hooks/useSDK";
import { useContexts } from "../../hooks/useContexts";

interface ManagePermissionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
}

interface Permission {
  id: number;
  name: string;
  label: string;
}

export const ManagePermissionsModal: React.FC<ManagePermissionsModalProps> = ({
  isOpen,
  onClose,
  user,
}) => {
  const { sdk } = useSDK();
  const { globalDispatch } = useContexts();
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [userPermissions, setUserPermissions] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen && user) {
      fetchPermissions();
      fetchUserPermissions(user.id);
    }
  }, [isOpen, user]);

  const fetchPermissions = async () => {
    try {
      const response = await sdk.callRawAPI(
        "/v2/api/ebadollar/custom/admin/permissions",
        {},
        "GET"
      );
      setPermissions(response.data);
    } catch (error) {
      console.error("Error fetching permissions:", error);
      globalDispatch({
        type: "SNACKBAR",
        payload: {
          message: "Error fetching permissions",
          toastStatus: "error",
        },
      });
    }
  };

  const fetchUserPermissions = async (userId: number) => {
    try {
      const response = await sdk.callRawAPI(
        `/v2/api/ebadollar/custom/admin/users/${userId}/permissions`,
        {},
        "GET"
      );
      setUserPermissions(response.data.map((p: Permission) => p.id));
    } catch (error) {
      console.error("Error fetching user permissions:", error);
      globalDispatch({
        type: "SNACKBAR",
        payload: {
          message: "Error fetching user permissions",
          toastStatus: "error",
        },
      });
    }
  };

  const handlePermissionChange = (permissionId: number) => {
    setUserPermissions((prev) =>
      prev.includes(permissionId)
        ? prev.filter((id) => id !== permissionId)
        : [...prev, permissionId]
    );
  };

  const handleUpdatePermissions = async () => {
    if (!user) return;
    setLoading(true);
    try {
      await sdk.callRawAPI(
        `/v2/api/ebadollar/custom/admin/users/${user.id}/permissions`,
        { permission_ids: userPermissions },
        "PUT"
      );
      globalDispatch({
        type: "SNACKBAR",
        payload: {
          message: "Permissions updated successfully!",
          toastStatus: "success",
        },
      });
      onClose();
    } catch (error) {
      console.error("Error updating permissions:", error);
      globalDispatch({
        type: "SNACKBAR",
        payload: {
          message: "Error updating permissions",
          toastStatus: "error",
        },
      });
    } finally {
      setLoading(false);
    }
  };

  if (!user) return null;

  return (
    <Modal
      isOpen={isOpen}
      modalCloseClick={onClose}
      title=""
      modalHeader={false}
      classes={{
        modalDialog: "w-fit max-w-none",
        modalContent: "p-0",
      }}
    >
      <div className="bg-white rounded-lg shadow-lg w-[400px]">
        {/* Header */}
        <div className="flex justify-between items-center p-6 pb-4">
          <h2 className="text-lg font-semibold text-gray-900">
            Manage permissions
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-xl leading-none"
          >
            ×
          </button>
        </div>

        {/* User Information Section */}
        <div className="px-6 pb-6">
          <h3 className="text-sm font-medium text-gray-600 mb-4">
            User information
          </h3>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <p className="text-xs text-gray-500 mb-1">Name</p>
              <p className="text-sm text-gray-900">
                {user.first_name} {user.last_name}
              </p>
            </div>
            <div>
              <p className="text-xs text-gray-500 mb-1">Email</p>
              <p className="text-sm text-gray-900">{user.email}</p>
            </div>
          </div>
        </div>

        {/* Permissions Section */}
        <div className="px-6 pb-6">
          <h3 className="text-sm font-medium text-gray-600 mb-4">
            Manage access to pages
          </h3>
          <div className="space-y-4">
            {permissions.map((permission) => (
              <div
                key={permission.id}
                className="flex justify-between items-center"
              >
                <label
                  htmlFor={`permission-${permission.id}`}
                  className="text-sm text-gray-700 cursor-pointer"
                >
                  {permission.label}
                </label>
                <div className="relative">
                  <input
                    type="checkbox"
                    id={`permission-${permission.id}`}
                    className="sr-only"
                    checked={userPermissions.includes(permission.id)}
                    onChange={() => handlePermissionChange(permission.id)}
                  />
                  <div
                    className={`w-5 h-5 border-2 rounded cursor-pointer flex items-center justify-center ${
                      userPermissions.includes(permission.id)
                        ? "bg-red-500 border-red-500"
                        : "border-gray-300 bg-white"
                    }`}
                    onClick={() => handlePermissionChange(permission.id)}
                  >
                    {userPermissions.includes(permission.id) && (
                      <svg
                        className="w-3 h-3 text-white"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Footer Buttons */}
        <div className="flex w-full justify-between gap-3 p-6 pt-4 border-t border-gray-100">
          <InteractiveButton
            onClick={onClose}
            className="px-4 py-2 text-sm !text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            Cancel
          </InteractiveButton>
          <InteractiveButton
            onClick={handleUpdatePermissions}
            className="px-6 py-2 text-sm text-white bg-gray-800 rounded hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-800"
            loading={loading}
          >
            OK
          </InteractiveButton>
        </div>
      </div>
    </Modal>
  );
};
