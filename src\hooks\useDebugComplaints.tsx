import { useState, useEffect } from "react";
import { useSDK } from "./useSDK";

export const useDebugComplaints = () => {
  const { sdk } = useSDK();
  const [complaints, setComplaints] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchComplaints = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await sdk.callRawAPI(
        "/v2/api/ebadollar/custom/admin/delivery-agent-complaints/debug/list",
        {},
        "GET"
      );
      
      if (!result.error) {
        setComplaints(result.data || []);
        console.log("Available complaint IDs:", result.data?.map((c: any) => c.id));
      } else {
        throw new Error(result.message || "Failed to fetch complaints");
      }
    } catch (err: any) {
      setError(err);
      console.error("Error fetching complaints:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchComplaints();
  }, []);

  return {
    complaints,
    loading,
    error,
    refetch: fetchComplaints
  };
};
