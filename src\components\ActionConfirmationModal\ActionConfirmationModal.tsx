import React from "react";
import { Modal } from "../Modal";
import { InteractiveButton } from "../InteractiveButton";

interface ActionConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  title: string;
  customMessage?: string;
  confirmText?: string;
  cancelText?: string;
  isLoading?: boolean;
}

export const ActionConfirmationModal: React.FC<
  ActionConfirmationModalProps
> = ({
  isOpen,
  onClose,
  onSuccess,
  title,
  customMessage,
  confirmText = "Confirm",
  cancelText = "Cancel",
  isLoading = false,
}) => {
  // Add debugging
  console.log("ActionConfirmationModal props:", {
    isOpen,
    title: typeof title,
    customMessage: typeof customMessage,
    confirmText: typeof confirmText,
    cancelText: typeof cancelText,
    isLoading: typeof isLoading,
  });

  if (!isOpen) {
    return null;
  }

  // Ensure all props are safe primitives
  const safeTitle = String(title || "");
  const safeCustomMessage = String(customMessage || "");
  const safeConfirmText = String(confirmText || "Confirm");
  const safeCancelText = String(cancelText || "Cancel");
  const safeIsLoading = Boolean(isLoading);

  return (
    <Modal
      isOpen={Boolean(isOpen)}
      modalCloseClick={onClose}
      title=""
      modalHeader={false}
      classes={{ modalDialog: " !max-w-md" }}
    >
      <div className="bg-white p-6 rounded-lg  mx-auto w-[90%] max-w-md">
        <h3 className="text-lg font-medium text-gray-900">{safeTitle}</h3>
        <div className="mt-2">
          <p className="text-sm text-gray-500">{safeCustomMessage}</p>
        </div>
        <div className="mt-6 flex justify-between space-x-4">
          <InteractiveButton
            onClick={onClose}
            className="bg-gray-200 text-gray-800"
          >
            {safeCancelText}
          </InteractiveButton>
          <InteractiveButton
            onClick={onSuccess}
            className="bg-blue-600 text-white"
            disabled={safeIsLoading}
          >
            {safeIsLoading ? "Loading..." : safeConfirmText}
          </InteractiveButton>
        </div>
      </div>
    </Modal>
  );
};

export default ActionConfirmationModal;
