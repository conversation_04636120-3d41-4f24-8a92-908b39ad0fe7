import { useState, useEffect, useCallback } from "react";
import { useParams } from "react-router-dom";
import { useSDK } from "../useSDK";

interface TransactionDetails {
  summary: {
    id: string;
    date: string;
    type: string;
    status: string;
    price: string;
    listing_id: number;
    listing_name: string;
    listing_description: string;
  };
  buyer: {
    id: number;
    name: string;
    credit_score: string;
    location: string;
    member_since: string;
    is_verified: boolean;
  };
  seller: {
    id: number;
    name: string;
    role: string;
    location: string;
    rating: {
      rating: number;
      review_count: number;
    };
    is_verified: boolean;
  };
  fees: {
    buyer: {
      gross_amount: number;
      usd_fee: number;
      eba_fee: number;
    };
    seller: {
      gross_amount: number;
      usd_fee: number;
      eba_fee: number;
    };
  };
  shipping: {
    method: string;
    status: string;
    tracking_number: string;
    confirmation_code: string;
    address: {
      street: string;
      city: string;
      state: string;
      zip: string;
      country: string;
    } | null;
  };
}

export const useTransactionDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { sdk } = useSDK();
  const [transactionDetails, setTransactionDetails] =
    useState<TransactionDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchDetails = useCallback(async () => {
    if (!id) {
      setLoading(false);
      setError(new Error("No transaction ID provided."));
      return;
    }
    setLoading(true);
    try {
      console.log("Fetching transaction details for ID:", id);
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/admin/transactions/${id}`,
        method: "GET",
      });
      console.log("Transaction details API response:", response);
      if (response.error) {
        console.error(
          "Transaction details API error:",
          response.error,
          response.message
        );
        throw new Error(
          response.message || "Failed to fetch transaction details"
        );
      }
      console.log("Setting transaction details:", response.data);
      setTransactionDetails(response.data);
    } catch (err: any) {
      console.error("Error in fetchDetails:", err);
      setError(err);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchDetails();
  }, [fetchDetails]);

  return { transactionDetails, loading, error, refetch: fetchDetails };
};
