import React, { memo, Suspense } from "react";
import { Skeleton } from "@/components/Skeleton";
import { BrandLogo } from "@/assets/images";
import { ViewMapType } from "@/components/ViewWrapper";

interface LazyLoadProps {
  children: React.ReactNode;
  counts?: number[];
  count?: number;
  className?: string;
  circle?: boolean;
  brand?: string | boolean;
  view?: ViewMapType["value"];
}

const LazyLoad = ({
  children,
  counts = [1],
  count = 1,
  className: propClassName,
  circle = false,
  brand = false,
  view: _view, // Prefix with underscore to indicate it's intentionally unused
}: LazyLoadProps) => {
  // Debug: Log all props to see what's being passed
  // console.log("LazyLoad props:", {
  //   children: typeof children,
  //   counts,
  //   count,
  //   className: propClassName,
  //   circle,
  //   brand: { type: typeof brand, value: brand },
  //   view: { type: typeof _view, value: _view },
  // });

  // Debug: Log the brand prop to see what's being passed
  if (typeof brand !== "string" && typeof brand !== "boolean") {
    console.error(
      "LazyLoad brand prop should be string or boolean, got:",
      typeof brand,
      brand
    );
  }
  const childrenArray = React.Children.toArray(children).filter(
    Boolean
  ) as React.ReactElement[];

  // Use the provided className prop if available, otherwise try to get it from children
  const className =
    propClassName ||
    (childrenArray.length > 0 &&
      String(childrenArray[0]?.props?.className || "")) ||
    "";

  // console.log("LazyLoad className computation:", {
  //   propClassName,
  //   childrenArrayLength: childrenArray.length,
  //   firstChildClassName: childrenArray[0]?.props?.className,
  //   finalClassName: className,
  // });

  return (
    <Suspense
      fallback={
        brand ? (
          // <div className="flex h-svh max-h-svh min-h-svh w-full min-w-full max-w-full flex-col items-center justify-center bg-[#1E293B] ">
          // {/* <img src={BrandLogo} className="!h-[12.25rem]" /> */}

          //    <span className="text-[2.8125rem] text-red-500">
          //      {String(brand || "")}
          //   </span>
          // </div>
          <>
          
          
          </>
         
        ) : (
          (() => {
            // console.log("Skeleton props:", {
            //   counts,
            //   count,
            //   className,
            //   circle,
            // });
            return (
              <Skeleton
                counts={counts}
                count={count}
                className={className}
                circle={circle}
              />
            );
          })()
        )
      }
    >
      {children}
    </Suspense>
  );
};

export default memo(LazyLoad);
