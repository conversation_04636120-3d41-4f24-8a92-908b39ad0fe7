import React, { useState } from "react";
import { Link } from "react-router-dom";

const FooterSection = () => {
  const [contactForm, setContactForm] = useState({
    email: "",
    message: ""
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setContactForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSendMessage = () => {
    // Handle form submission
    console.log("Contact form submitted:", contactForm);
    // Reset form
    setContactForm({ email: "", message: "" });
  };

  return (
    <footer className="bg-gray-50 py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* eBaDollar Column */}
          <div>
            <h3 className="font-bold text-lg mb-4" style={{ color: "#0D3166" }}>
              eBaDollar
            </h3>
            <p className="text-gray-600 text-sm leading-relaxed">
              Trade what you have for what you want without worrying about money!
            </p>
          </div>

          {/* Quick Links Column */}
          <div>
            <h3 className="font-bold text-lg mb-4" style={{ color: "#0D3166" }}>
              Quick Links
            </h3>
            <ul className="space-y-2">
              <li>
                <Link to="/how-it-works" className="text-gray-600 text-sm hover:text-gray-800">
                  How It Works
                </Link>
              </li>
              <li>
                <Link to="/benefits" className="text-gray-600 text-sm hover:text-gray-800">
                  Benefits
                </Link>
              </li>
              <li>
                <Link to="/faqs" className="text-gray-600 text-sm hover:text-gray-800">
                  FAQs
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-gray-600 text-sm hover:text-gray-800">
                  About Us
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal Column */}
          <div>
            <h3 className="font-bold text-lg mb-4" style={{ color: "#0D3166" }}>
              Legal
            </h3>
            <ul className="space-y-2">
              <li>
                <Link to="/terms" className="text-gray-600 text-sm hover:text-gray-800">
                  Terms of Use
                </Link>
              </li>
              <li>
                <Link to="/privacy" className="text-gray-600 text-sm hover:text-gray-800">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link to="/cookies" className="text-gray-600 text-sm hover:text-gray-800">
                  Cookie Policy
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Us Column */}
          <div>
            <h3 className="font-bold text-lg mb-4" style={{ color: "#0D3166" }}>
              Contact Us
            </h3>
            <div className="space-y-3">
              <input
                type="email"
                name="email"
                placeholder="Your email"
                value={contactForm.email}
                onChange={handleInputChange}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
              <textarea
                name="message"
                placeholder="Your message"
                rows={3}
                value={contactForm.message}
                onChange={handleInputChange}
                className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-none"
              />
              <button
                onClick={handleSendMessage}
                className="w-full text-white text-sm font-medium py-2 px-4 rounded-md hover:opacity-90 transition-opacity duration-200"
                style={{ backgroundColor: "#F52D2A" }}
              >
                Send
              </button>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-12 pt-8 border-t border-gray-200 text-center">
          <p className="text-gray-500 text-sm">
            © 2023 eBaDollar. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default FooterSection;
