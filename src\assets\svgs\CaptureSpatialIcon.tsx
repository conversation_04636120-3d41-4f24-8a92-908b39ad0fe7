import React from 'react';

const CaptureSpatialIcon = ({ className = "", fill = "#A8A8A8", onClick = () => { } }) => {
    return (
        <svg onClick={onClick} className={`${className}`} xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path d="M9.27277 1.15563C9.72294 0.896692 10.2768 0.896692 10.727 1.15563L14.0417 3.06223L7.08205 7.0804V2.41572L9.27277 1.15563Z" fill={fill} />
            <path d="M5.83205 3.13472L2.6811 4.94713C2.22874 5.20733 1.94989 5.68941 1.94989 6.21126V8.9293L5.83205 11.1707V3.13472Z" fill={fill} />
            <path d="M1.94989 10.3727V13.7889C1.94989 14.3107 2.22874 14.7928 2.6811 15.053L5.06487 16.4241L8.74811 14.2976L1.94989 10.3727Z" fill={fill} />
            <path d="M6.3172 17.1445L9.27277 18.8445C9.72294 19.1035 10.2768 19.1035 10.727 18.8445L12.9154 17.5858V13.335L6.3172 17.1445Z" fill={fill} />
            <path d="M14.1654 16.8668L17.3187 15.053C17.771 14.7928 18.0499 14.3107 18.0499 13.7889V11.4889L14.1654 9.24617V16.8668Z" fill={fill} />
            <path d="M18.0499 10.0455V6.21126C18.0499 5.68941 17.771 5.20733 17.3187 4.94713L15.2941 3.78257L11.2481 6.1185L18.0499 10.0455Z" fill={fill} />
            <path d="M9.99811 13.5759L7.08205 11.8924V8.52377L9.99811 6.84019L12.9154 8.52448V11.8916L9.99811 13.5759Z" fill={fill} />
        </svg>
    );
};

export default CaptureSpatialIcon;