import React, { useState } from "react";

interface ProvideShippingInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (shippingInfo: {
    carrierName: string;
    trackingNumber: string;
    shippingDate: string;
    proofFile?: File;
  }) => void;
}

const ProvideShippingInfoModal: React.FC<ProvideShippingInfoModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  const [carrierName, setCarrierName] = useState("");
  const [trackingNumber, setTrackingNumber] = useState("");
  const [shippingDate, setShippingDate] = useState("");
  const [proofFile, setProofFile] = useState<File | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      carrierName,
      trackingNumber,
      shippingDate,
      proofFile: proofFile || undefined,
    });
    // Reset form
    setCarrierName("");
    setTrackingNumber("");
    setShippingDate("");
    setProofFile(null);
    onClose();
  };

  const handleFileUpload = (file: File) => {
    if (file.size <= 5 * 1024 * 1024) { // 5MB limit
      setProofFile(file);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleBrowseClick = () => {
    const fileInput = document.getElementById('proof-file-input') as HTMLInputElement;
    fileInput?.click();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-[480px] max-w-[90vw] max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-[#0F2C59]">
            Provide Shipping Info
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl font-light"
          >
            ×
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Carrier Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Carrier Name
            </label>
            <input
              type="text"
              value={carrierName}
              onChange={(e) => setCarrierName(e.target.value)}
              placeholder="e.g., FedEx, DHL"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              required
            />
          </div>

          {/* Tracking Number */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tracking Number
            </label>
            <input
              type="text"
              value={trackingNumber}
              onChange={(e) => setTrackingNumber(e.target.value)}
              placeholder="Enter tracking number"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm"
              required
            />
          </div>

          {/* Shipping Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Shipping Date (Optional)
            </label>
            <div className="relative">
              <input
                type="date"
                value={shippingDate}
                onChange={(e) => setShippingDate(e.target.value)}
                placeholder="mm/dd/yyyy"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#0F2C59] focus:border-transparent text-sm pr-10"
              />
              <div className="absolute inset-y-0 right-3 flex items-center pointer-events-none">
                <svg
                  className="w-4 h-4 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
              </div>
            </div>
          </div>

          {/* Upload Proof */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Upload Proof (Optional)
            </label>
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                isDragOver
                  ? "border-[#0F2C59] bg-blue-50"
                  : "border-gray-300 bg-gray-50"
              }`}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
            >
              <div className="flex flex-col items-center space-y-3">
                <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                  <svg
                    className="w-6 h-6 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                    />
                  </svg>
                </div>
                <div className="text-sm text-gray-600">
                  <span>Drop screenshot or receipt here, or </span>
                  <button
                    type="button"
                    onClick={handleBrowseClick}
                    className="text-[#0F2C59] hover:text-blue-800 font-medium"
                  >
                    browse
                  </button>
                </div>
                <div className="text-xs text-gray-500">
                  Supports: JPG, PNG, PDF (Max 5MB)
                </div>
                {proofFile && (
                  <div className="text-sm text-[#16A34A] font-medium">
                    ✓ {proofFile.name} uploaded
                  </div>
                )}
              </div>
            </div>
            <input
              id="proof-file-input"
              type="file"
              accept=".jpg,.jpeg,.png,.pdf"
              onChange={handleFileInputChange}
              className="hidden"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0F2C59]"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-2 text-sm font-medium text-white bg-[#0F2C59] rounded-md hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0F2C59]"
            >
              Submit Shipping Info
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProvideShippingInfoModal;
