import React from "react";

const EarnWithDeliverySection = () => {
  return (
    <section 
      className="py-20 px-4 sm:px-6 lg:px-8"
      style={{
        background: "linear-gradient(135deg, #0D3166 0%, #1E40AF 100%)"
      }}
    >
      <div className="max-w-7xl mx-auto">
        <div className="max-w-4xl mx-auto">
          {/* Card Container */}
          <div 
            className="bg-white bg-opacity-10 backdrop-blur-sm rounded-3xl p-12 text-center"
            style={{
              border: "1px solid rgba(255, 255, 255, 0.2)"
            }}
          >
            {/* Delivery Truck Icon */}
            <div className="flex justify-center mb-8">
              <div 
                className="w-24 h-24 rounded-full flex items-center justify-center"
                style={{ backgroundColor: "#4F46E5" }}
              >
                <svg className="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 7c0-1.1-.9-2-2-2h-3v2h3v2.65L13.52 14H10V9H6c-2.21 0-4 1.79-4 4v3h2c0 1.66 1.34 3 3 3s3-1.34 3-3h4.48L19 10.35V7zM7 17c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/>
                  <path d="M5 6h5v2H5zm0-2h5v2H5z"/>
                </svg>
              </div>
            </div>

            {/* Main Heading */}
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Earn with eBaDelivery
            </h2>

            {/* Description */}
            <p className="text-xl text-blue-100 mb-10 leading-relaxed max-w-2xl mx-auto">
              Join the eBaDelivery network and earn eBaDollars by delivering goods 
              between neighbours. Perfect for flexible, local side income.
            </p>

            {/* Benefits List */}
            <div className="space-y-4 mb-10 text-left max-w-2xl mx-auto">
              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5 text-green-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                </svg>
                <span className="text-blue-100 text-lg">Set your own schedule and delivery radius</span>
              </div>

              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5 text-green-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                </svg>
                <span className="text-blue-100 text-lg">Earn eBaDollars for each successful delivery</span>
              </div>

              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5 text-green-400 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"/>
                </svg>
                <span className="text-blue-100 text-lg">Build your reputation and increase your earning potential</span>
              </div>
            </div>

            {/* CTA Button */}
            <button 
              className="px-8 py-3 text-white font-semibold rounded-md transition-colors hover:opacity-90 text-lg"
              style={{ backgroundColor: "#F52D2A" }}
            >
              Start Earning
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default EarnWithDeliverySection;
