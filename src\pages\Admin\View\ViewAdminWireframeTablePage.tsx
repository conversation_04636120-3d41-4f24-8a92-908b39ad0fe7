import React from "react";
import { ViewModelItem, ViewModelItemProps } from "@/components/ViewModelItem";
import { Models } from "@/utils/baas";
import { useViewModelHook } from "@/hooks/useViewModelHook";

interface ViewProjectPageProps {
  activeId: string | number;
  onClose?: (e?: any) => void;
}

const ViewAdminWireframeTablePage: React.FC<ViewProjectPageProps> = ({
  activeId
}: ViewProjectPageProps) => {
  const { data: viewModel, isLoading } = useViewModelHook(
    Models.PROJECT,
    activeId
  );

  const viewModelItems: ViewModelItemProps[] = [
    {
      name: "Id",
      value: viewModel?.id,
      icon: () => (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M1.66669 3.33317C1.66669 2.4127 2.41288 1.6665 3.33335 1.6665H9.65484C10.0969 1.6665 10.5208 1.8421 10.8334 2.15466L17.9167 9.23799C18.5676 9.88887 18.5676 10.9441 17.9167 11.595L11.5952 17.9165C10.9443 18.5674 9.88905 18.5674 9.23818 17.9165L2.15484 10.8332C1.84228 10.5206 1.66669 10.0967 1.66669 9.65466V3.33317ZM6.25002 7.49984C6.94038 7.49984 7.50002 6.94019 7.50002 6.24984C7.50002 5.55948 6.94038 4.99984 6.25002 4.99984C5.55966 4.99984 5.00002 5.55948 5.00002 6.24984C5.00002 6.94019 5.55966 7.49984 6.25002 7.49984Z"
            fill="#A8A8A8"
          />
        </svg>
      )
    },
    {
      name: "Name",
      value: viewModel?.name,
      icon: () => (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M1.66669 3.33317C1.66669 2.4127 2.41288 1.6665 3.33335 1.6665H9.65484C10.0969 1.6665 10.5208 1.8421 10.8334 2.15466L17.9167 9.23799C18.5676 9.88887 18.5676 10.9441 17.9167 11.595L11.5952 17.9165C10.9443 18.5674 9.88905 18.5674 9.23818 17.9165L2.15484 10.8332C1.84228 10.5206 1.66669 10.0967 1.66669 9.65466V3.33317ZM6.25002 7.49984C6.94038 7.49984 7.50002 6.94019 7.50002 6.24984C7.50002 5.55948 6.94038 4.99984 6.25002 4.99984C5.55966 4.99984 5.00002 5.55948 5.00002 6.24984C5.00002 6.94019 5.55966 7.49984 6.25002 7.49984Z"
            fill="#A8A8A8"
          />
        </svg>
      )
    },
    {
      name: "Slug",
      value: viewModel?.slug,
      icon: () => (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
        >
          <path
            d="M9.75027 5.52371L10.7168 4.55722C13.1264 2.14759 17.0332 2.14759 19.4428 4.55722C21.8524 6.96684 21.8524 10.8736 19.4428 13.2832L18.4742 14.2519M5.52886 9.74513L4.55722 10.7168C2.14759 13.1264 2.1476 17.0332 4.55722 19.4428C6.96684 21.8524 10.8736 21.8524 13.2832 19.4428L14.2478 18.4782M9.5 14.5L14.5 9.5"
            stroke="#A8A8A8"
            strokeWidth="1.5"
            strokeLinecap="round"
          />
        </svg>
      )
    },
    {
      name: "Hostname",
      value: viewModel?.hostname,
      hasCopy: true,
      icon: () => (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12 2C7.58172 2 4 5.57095 4 9.97594V20.0025C4 21.6244 5.83874 22.5678 7.16248 21.6251L8.62332 20.5848L11.2122 21.6909C11.7153 21.9059 12.2847 21.9059 12.7878 21.6909L15.3767 20.5848L16.8375 21.6251C18.1613 22.5678 20 21.6244 20 20.0025V9.97594C20 5.57095 16.4183 2 12 2ZM11 10.5C11 11.3284 10.3284 12 9.5 12C8.67157 12 8 11.3284 8 10.5C8 9.67157 8.67157 9 9.5 9C10.3284 9 11 9.67157 11 10.5ZM14.5 12C15.3284 12 16 11.3284 16 10.5C16 9.67157 15.3284 9 14.5 9C13.6716 9 13 9.67157 13 10.5C13 11.3284 13.6716 12 14.5 12Z"
            fill="#A8A8A8"
          />
        </svg>
      )
    },
    {
      name: "Api Domain",
      value: viewModel?.api_domain,
      hasTopBorder: true,
      topHeader: "Domains",
      icon: () => (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <path
            d="M6.24998 1.6665C4.63915 1.6665 3.33331 2.97234 3.33331 4.58317C3.33331 5.90443 4.21186 7.02049 5.41665 7.37905V12.6206C4.21186 12.9792 3.33331 14.0952 3.33331 15.4165C3.33331 17.0273 4.63915 18.3332 6.24998 18.3332C7.86081 18.3332 9.16665 17.0273 9.16665 15.4165C9.16665 14.0952 8.2881 12.9792 7.08331 12.6206V10.8332H12.9166C13.8371 10.8332 14.5833 10.087 14.5833 9.1665V7.37905C15.7881 7.02049 16.6666 5.90443 16.6666 4.58317C16.6666 2.97234 15.3608 1.6665 13.75 1.6665C12.1391 1.6665 10.8333 2.97234 10.8333 4.58317C10.8333 5.90443 11.7119 7.02049 12.9166 7.37905V9.1665H7.08331V7.37905C8.2881 7.02049 9.16665 5.90443 9.16665 4.58317C9.16665 2.97234 7.86081 1.6665 6.24998 1.6665Z"
            fill="#A8A8A8"
          />
        </svg>
      )
    },
    {
      name: "Frontend Domain",
      value: viewModel?.frontend_domain,
      icon: () => (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <path
            d="M5.00002 5.8335C4.53978 5.8335 4.16669 6.20659 4.16669 6.66683C4.16669 7.12707 4.53978 7.50016 5.00002 7.50016C5.46026 7.50016 5.83335 7.12707 5.83335 6.66683C5.83335 6.20659 5.46026 5.8335 5.00002 5.8335Z"
            fill="#A8A8A8"
          />
          <path
            d="M6.66669 6.66683C6.66669 6.20659 7.03978 5.8335 7.50002 5.8335C7.96026 5.8335 8.33335 6.20659 8.33335 6.66683C8.33335 7.12707 7.96026 7.50016 7.50002 7.50016C7.03978 7.50016 6.66669 7.12707 6.66669 6.66683Z"
            fill="#A8A8A8"
          />
          <path
            d="M10 5.8335C9.53978 5.8335 9.16669 6.20659 9.16669 6.66683C9.16669 7.12707 9.53978 7.50016 10 7.50016C10.4603 7.50016 10.8334 7.12707 10.8334 6.66683C10.8334 6.20659 10.4603 5.8335 10 5.8335Z"
            fill="#A8A8A8"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M1.66669 5.00016C1.66669 4.07969 2.41288 3.3335 3.33335 3.3335H16.6667C17.5872 3.3335 18.3334 4.07969 18.3334 5.00016V15.0002C18.3334 15.9206 17.5872 16.6668 16.6667 16.6668H3.33335C2.41288 16.6668 1.66669 15.9206 1.66669 15.0002V5.00016ZM3.33335 5.00016L16.6667 5.00016V8.3335H3.33335V5.00016Z"
            fill="#A8A8A8"
          />
        </svg>
      )
    },
    {
      name: "Api Domain Id",
      value: viewModel?.api_domain_id,
      hasTopBorder: true,
      topHeader: "Deployment",
      icon: () => (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <path
            d="M6.24998 1.6665C4.63915 1.6665 3.33331 2.97234 3.33331 4.58317C3.33331 5.90443 4.21186 7.02049 5.41665 7.37905V12.6206C4.21186 12.9792 3.33331 14.0952 3.33331 15.4165C3.33331 17.0273 4.63915 18.3332 6.24998 18.3332C7.86081 18.3332 9.16665 17.0273 9.16665 15.4165C9.16665 14.0952 8.2881 12.9792 7.08331 12.6206V10.8332H12.9166C13.8371 10.8332 14.5833 10.087 14.5833 9.1665V7.37905C15.7881 7.02049 16.6666 5.90443 16.6666 4.58317C16.6666 2.97234 15.3608 1.6665 13.75 1.6665C12.1391 1.6665 10.8333 2.97234 10.8333 4.58317C10.8333 5.90443 11.7119 7.02049 12.9166 7.37905V9.1665H7.08331V7.37905C8.2881 7.02049 9.16665 5.90443 9.16665 4.58317C9.16665 2.97234 7.86081 1.6665 6.24998 1.6665Z"
            fill="#A8A8A8"
          />
        </svg>
      )
    },
    {
      name: "Frontend Domain Id",
      value: viewModel?.frontend_domain_id,
      icon: () => (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <path
            d="M5.00002 5.8335C4.53978 5.8335 4.16669 6.20659 4.16669 6.66683C4.16669 7.12707 4.53978 7.50016 5.00002 7.50016C5.46026 7.50016 5.83335 7.12707 5.83335 6.66683C5.83335 6.20659 5.46026 5.8335 5.00002 5.8335Z"
            fill="#A8A8A8"
          />
          <path
            d="M6.66669 6.66683C6.66669 6.20659 7.03978 5.8335 7.50002 5.8335C7.96026 5.8335 8.33335 6.20659 8.33335 6.66683C8.33335 7.12707 7.96026 7.50016 7.50002 7.50016C7.03978 7.50016 6.66669 7.12707 6.66669 6.66683Z"
            fill="#A8A8A8"
          />
          <path
            d="M10 5.8335C9.53978 5.8335 9.16669 6.20659 9.16669 6.66683C9.16669 7.12707 9.53978 7.50016 10 7.50016C10.4603 7.50016 10.8334 7.12707 10.8334 6.66683C10.8334 6.20659 10.4603 5.8335 10 5.8335Z"
            fill="#A8A8A8"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M1.66669 5.00016C1.66669 4.07969 2.41288 3.3335 3.33335 3.3335H16.6667C17.5872 3.3335 18.3334 4.07969 18.3334 5.00016V15.0002C18.3334 15.9206 17.5872 16.6668 16.6667 16.6668H3.33335C2.41288 16.6668 1.66669 15.9206 1.66669 15.0002V5.00016ZM3.33335 5.00016L16.6667 5.00016V8.3335H3.33335V5.00016Z"
            fill="#A8A8A8"
          />
        </svg>
      )
    },
    {
      name: "Wireframe Id",
      value: viewModel?.wireframe_id,
      icon: () => (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <path
            d="M3.33335 2.5C2.41288 2.5 1.66669 3.24619 1.66669 4.16667V10H18.3334V4.16667C18.3334 3.24619 17.5872 2.5 16.6667 2.5H3.33335Z"
            fill="#A8A8A8"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M1.66669 13.3333V11.6667H18.3334V13.3333C18.3334 14.2538 17.5872 15 16.6667 15H12.5V17.5C12.5 17.9602 12.1269 18.3333 11.6667 18.3333H8.33335C7.87312 18.3333 7.50002 17.9602 7.50002 17.5V15H3.33335C2.41288 15 1.66669 14.2538 1.66669 13.3333ZM9.16669 15V16.6667H10.8334V15H9.16669Z"
            fill="#A8A8A8"
          />
        </svg>
      )
    },
    {
      name: "Configuration",
      value: viewModel?.configuration,
      hasTopBorder: true,
      icon: () => (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M8.33017 2.39057C8.63174 1.93821 9.13943 1.6665 9.6831 1.6665H10.3169C10.8606 1.6665 11.3683 1.93821 11.6699 2.39057L12.6209 3.81706L14.0783 3.48072C14.6246 3.35466 15.1973 3.5189 15.5937 3.91533L16.0845 4.40612C16.481 4.80255 16.6452 5.37524 16.5191 5.92151L16.1828 7.37899L17.6093 8.32999C18.0616 8.63156 18.3334 9.13925 18.3334 9.68291V10.3168C18.3334 10.8604 18.0616 11.3681 17.6093 11.6697L16.1828 12.6207L16.5191 14.0782C16.6452 14.6244 16.481 15.1971 16.0845 15.5936L15.5937 16.0843C15.1973 16.4808 14.6246 16.645 14.0783 16.519L12.6209 16.1826L11.6699 17.6091C11.3683 18.0615 10.8606 18.3332 10.3169 18.3332H9.6831C9.13943 18.3332 8.63174 18.0615 8.33017 17.6091L7.37917 16.1826L5.9217 16.519C5.37542 16.645 4.80273 16.4808 4.40631 16.0843L3.91551 15.5936C3.51909 15.1971 3.35484 14.6244 3.4809 14.0782L3.81724 12.6207L2.39075 11.6697C1.9384 11.3681 1.66669 10.8604 1.66669 10.3168V9.68291C1.66669 9.13925 1.9384 8.63156 2.39075 8.32999L3.81724 7.37899L3.4809 5.92151C3.35484 5.37524 3.51908 4.80255 3.91551 4.40612L4.40631 3.91533C4.80273 3.5189 5.37542 3.35466 5.9217 3.48072L7.37917 3.81706L8.33017 2.39057ZM7.08335 9.99984C7.08335 8.38901 8.38919 7.08317 10 7.08317C11.6109 7.08317 12.9167 8.38901 12.9167 9.99984C12.9167 11.6107 11.6109 12.9165 10 12.9165C8.38919 12.9165 7.08335 11.6107 7.08335 9.99984Z"
            fill="#A8A8A8"
          />
        </svg>
      ),
      hasCopy: true
    },
    {
      name: "Validation",
      value: viewModel?.validation,
      icon: () => (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M10 4.16667C6.77834 4.16667 4.16667 6.77834 4.16667 10C4.16667 11.9813 5.15438 13.7331 6.66667 14.7879V12.5C6.66667 12.0398 7.03976 11.6667 7.5 11.6667C7.96024 11.6667 8.33333 12.0398 8.33333 12.5V16.6667C8.33333 17.1269 7.96024 17.5 7.5 17.5H3.33333C2.8731 17.5 2.5 17.1269 2.5 16.6667C2.5 16.2064 2.8731 15.8333 3.33333 15.8333H5.28577C3.58729 14.459 2.5 12.357 2.5 10C2.5 5.85786 5.85786 2.5 10 2.5C10.9931 2.5 11.943 2.69345 12.8127 3.04548C13.2393 3.21817 13.4451 3.704 13.2724 4.13061C13.0998 4.55722 12.6139 4.76307 12.1873 4.59038C11.5129 4.3174 10.7751 4.16667 10 4.16667Z"
            fill="#A8A8A8"
          />
          <path
            d="M10.8333 17.5C11.2936 17.5 11.6667 17.1269 11.6667 16.6667C11.6667 16.2064 11.2936 15.8333 10.8333 15.8333C10.3731 15.8333 10 16.2064 10 16.6667C10 17.1269 10.3731 17.5 10.8333 17.5Z"
            fill="#A8A8A8"
          />
          <path
            d="M17.5 9.16666C17.5 8.70642 17.1269 8.33332 16.6667 8.33332C16.2064 8.33332 15.8333 8.70642 15.8333 9.16666C15.8333 9.62689 16.2064 9.99999 16.6667 9.99999C17.1269 9.99999 17.5 9.62689 17.5 9.16666Z"
            fill="#A8A8A8"
          />
          <path
            d="M16.6079 11.8899C17.0065 12.1201 17.143 12.6297 16.9129 13.0283C16.6828 13.4269 16.1731 13.5634 15.7746 13.3333C15.376 13.1032 15.2394 12.5935 15.4695 12.195C15.6996 11.7964 16.2093 11.6598 16.6079 11.8899Z"
            fill="#A8A8A8"
          />
          <path
            d="M14.473 16.0785C14.8716 15.8484 15.0081 15.3387 14.778 14.9401C14.5479 14.5416 14.0382 14.405 13.6397 14.6351C13.2411 14.8652 13.1045 15.3749 13.3347 15.7735C13.5648 16.172 14.0744 16.3086 14.473 16.0785Z"
            fill="#A8A8A8"
          />
          <path
            d="M15.7725 6.66665C15.3739 6.89677 14.8642 6.76021 14.6341 6.36163C14.404 5.96305 14.5405 5.45339 14.9391 5.22328C15.3377 4.99316 15.8474 5.12972 16.0775 5.5283C16.3076 5.92687 16.171 6.43653 15.7725 6.66665Z"
            fill="#A8A8A8"
          />
        </svg>
      ),
      hasCopy: true
    },
    {
      name: "Created At",
      value: viewModel?.created_at,
      icon: () => (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <path
            d="M6.66667 1.6665C7.1269 1.6665 7.5 2.0396 7.5 2.49984V3.33317H12.5V2.49984C12.5 2.0396 12.8731 1.6665 13.3333 1.6665C13.7936 1.6665 14.1667 2.0396 14.1667 2.49984V3.33317H15.8333C16.7538 3.33317 17.5 4.07936 17.5 4.99984V7.49984H2.5V4.99984C2.5 4.07936 3.24619 3.33317 4.16667 3.33317H5.83333V2.49984C5.83333 2.0396 6.20643 1.6665 6.66667 1.6665Z"
            fill="#A8A8A8"
          />
          <path
            d="M2.5 15.8332V9.1665H17.5V15.8332C17.5 16.7536 16.7538 17.4998 15.8333 17.4998H4.16667C3.24619 17.4998 2.5 16.7536 2.5 15.8332Z"
            fill="#A8A8A8"
          />
        </svg>
      )
    },
    {
      name: "Updated At",
      value: viewModel?.updated_at,
      icon: () => (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <path
            d="M6.66667 1.6665C7.1269 1.6665 7.5 2.0396 7.5 2.49984V3.33317H12.5V2.49984C12.5 2.0396 12.8731 1.6665 13.3333 1.6665C13.7936 1.6665 14.1667 2.0396 14.1667 2.49984V3.33317H15.8333C16.7538 3.33317 17.5 4.07936 17.5 4.99984V7.49984H2.5V4.99984C2.5 4.07936 3.24619 3.33317 4.16667 3.33317H5.83333V2.49984C5.83333 2.0396 6.20643 1.6665 6.66667 1.6665Z"
            fill="#A8A8A8"
          />
          <path
            d="M2.5 15.8332V9.1665H17.5V15.8332C17.5 16.7536 16.7538 17.4998 15.8333 17.4998H4.16667C3.24619 17.4998 2.5 16.7536 2.5 15.8332Z"
            fill="#A8A8A8"
          />
        </svg>
      )
    }
  ];

  return (
    <div className=" mx-auto rounded">
      {/* <div
        className={`flex items-center p-3 gap-4 border-b border-b-[#E0E0E0] justify-between`}
      >
        <div className="flex items-center gap-3">
          <svg
            onClick={() => onClose()}
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M14.3322 5.83203L19.8751 11.3749C20.2656 11.7654 20.2656 12.3986 19.8751 12.7891L14.3322 18.332M19.3322 12.082H3.83218"
              stroke="#A8A8A8"
              stroke-width="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
          <span className="text-lg font-semibold">View Project</span>
        </div>
      </div> */}
      <div>
        {/* <hr className="my-1 border-b border-b-gray-400" />
            <div className="text-">Domains</div> */}
        {viewModelItems &&
          viewModelItems.map((item, index) => {
            return (
              <ViewModelItem
                key={index}
                name={item.name}
                value={item.value}
                icon={item.icon}
                hasCopy={item.hasCopy}
                hasTopBorder={item.hasTopBorder}
                topHeader={item.topHeader}
                isLoading={isLoading}
              />
            );
          })}
      </div>
    </div>
  );
};

export default ViewAdminWireframeTablePage;
