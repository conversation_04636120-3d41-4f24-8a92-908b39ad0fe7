import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import MkdSDK from "../utils/MkdSDK";

export interface StripePaymentMethod {
  id: string;
  object: string;
  type: string;
  card?: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
    funding: string;
    country: string;
  };
  customer?: string;
  created: number;
  livemode: boolean;
}

export interface PaymentMethodsResponse {
  payment_methods: StripePaymentMethod[];
  has_more: boolean;
}

const useCustomerPaymentMethods = () => {
  const queryClient = useQueryClient();
  const sdk = new MkdSDK();

  // Query to fetch payment methods
  const {
    data: paymentMethodsData,
    isLoading,
    error,
    refetch,
  } = useQuery<PaymentMethodsResponse>({
    queryKey: ["paymentMethods"],
    queryFn: async () => {
      const response = await sdk.listCustomerPaymentMethods({
        type: "card",
        limit: 10,
      });

      if (response.error) {
        throw new Error(response.message || "Failed to fetch payment methods");
      }

      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Mutation to attach payment method
  const attachPaymentMethod = useMutation({
    mutationFn: async (paymentMethodId: string) => {
      const response = await sdk.attachPaymentMethodToCustomer({
        payment_method_id: paymentMethodId,
      });

      if (response.error) {
        throw new Error(response.message || "Failed to attach payment method");
      }

      return response.data;
    },
    onSuccess: () => {
      // Invalidate and refetch payment methods
      queryClient.invalidateQueries({ queryKey: ["paymentMethods"] });
    },
  });

  // Mutation to detach payment method
  const detachPaymentMethod = useMutation({
    mutationFn: async (paymentMethodId: string) => {
      const response = await sdk.detachPaymentMethodFromCustomer({
        payment_method_id: paymentMethodId,
      });

      if (response.error) {
        throw new Error(response.message || "Failed to detach payment method");
      }

      return response.data;
    },
    onSuccess: () => {
      // Invalidate and refetch payment methods
      queryClient.invalidateQueries({ queryKey: ["paymentMethods"] });
    },
  });

  // Legacy payment methods query (for backward compatibility)
  const {
    data: legacyPaymentMethods,
    isLoading: isLoadingLegacy,
    error: legacyError,
  } = useQuery({
    queryKey: ["legacyPaymentMethods"],
    queryFn: async () => {
      const response = await sdk.request({
        endpoint: "/v2/api/ebadollar/custom/member/payment-methods",
        method: "GET",
      });

      if (response.error) {
        throw new Error(
          response.message || "Failed to fetch legacy payment methods"
        );
      }

      return response.data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Mutation to set default payment method (legacy)
  const setDefaultPaymentMethod = useMutation({
    mutationFn: async (methodId: string) => {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/payment-methods/${methodId}/set-default`,
        method: "PUT",
      });

      if (response.error) {
        throw new Error(
          response.message || "Failed to set default payment method"
        );
      }

      return response.data;
    },
    onSuccess: () => {
      // Invalidate both payment method queries
      queryClient.invalidateQueries({ queryKey: ["paymentMethods"] });
      queryClient.invalidateQueries({ queryKey: ["legacyPaymentMethods"] });
    },
  });

  // Mutation to remove legacy payment method
  const removeLegacyPaymentMethod = useMutation({
    mutationFn: async (methodId: string) => {
      const response = await sdk.request({
        endpoint: `/v2/api/ebadollar/custom/member/payment-methods/${methodId}`,
        method: "DELETE",
      });

      if (response.error) {
        throw new Error(response.message || "Failed to remove payment method");
      }

      return response.data;
    },
    onSuccess: () => {
      // Invalidate both payment method queries
      queryClient.invalidateQueries({ queryKey: ["paymentMethods"] });
      queryClient.invalidateQueries({ queryKey: ["legacyPaymentMethods"] });
    },
  });

  // Helper function to format card brand
  const formatCardBrand = (brand: string): string => {
    switch (brand.toLowerCase()) {
      case "visa":
        return "Visa";
      case "mastercard":
        return "Mastercard";
      case "amex":
        return "American Express";
      case "discover":
        return "Discover";
      case "diners":
        return "Diners Club";
      case "jcb":
        return "JCB";
      case "unionpay":
        return "UnionPay";
      default:
        return brand.charAt(0).toUpperCase() + brand.slice(1);
    }
  };

  // Helper function to get card icon
  const getCardIcon = (brand: string): string => {
    switch (brand.toLowerCase()) {
      case "visa":
        return "💳";
      case "mastercard":
        return "💳";
      case "amex":
        return "💳";
      case "discover":
        return "💳";
      default:
        return "💳";
    }
  };

  return {
    // Stripe PaymentMethods
    paymentMethods: paymentMethodsData?.payment_methods || [],
    hasMorePaymentMethods: paymentMethodsData?.has_more || false,
    isLoadingPaymentMethods: isLoading,
    paymentMethodsError: error,

    // Legacy payment methods
    legacyPaymentMethods: legacyPaymentMethods || [],
    isLoadingLegacyPaymentMethods: isLoadingLegacy,
    legacyPaymentMethodsError: legacyError,

    // Actions
    attachPaymentMethod,
    detachPaymentMethod,
    setDefaultPaymentMethod,
    removeLegacyPaymentMethod,
    refetchPaymentMethods: refetch,

    // Helpers
    formatCardBrand,
    getCardIcon,

    // Loading states
    isAttaching: attachPaymentMethod.isPending,
    isDetaching: detachPaymentMethod.isPending,
    isSettingDefault: setDefaultPaymentMethod.isPending,
    isRemoving: removeLegacyPaymentMethod.isPending,
  };
};

export default useCustomerPaymentMethods;
