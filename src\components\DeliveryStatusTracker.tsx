import React, { useState } from "react";
import {
  useDeliveryStatusHistoryQuery,
  useDeliveryStatusUpdateMutation,
  formatDeliveryStatus,
  getDeliveryStatusColor,
  getNextValidStatus,
  canUpdateStatus,
  IDeliveryStatusUpdate,
} from "../query/useDeliveryCalculation";

interface IDeliveryStatusTrackerProps {
  taskId: number;
  currentStatus: string;
  isDeliveryAgent?: boolean;
  onStatusUpdate?: (newStatus: string) => void;
  className?: string;
}

export const DeliveryStatusTracker: React.FC<IDeliveryStatusTrackerProps> = ({
  taskId,
  currentStatus,
  isDeliveryAgent = false,
  onStatusUpdate,
  className = "",
}) => {
  const [showUpdateForm, setShowUpdateForm] = useState(false);
  const [updateData, setUpdateData] = useState<IDeliveryStatusUpdate>({
    status: currentStatus as any,
    location: "",
    notes: "",
  });

  const {
    data: statusHistory,
    isLoading,
    refetch,
  } = useDeliveryStatusHistoryQuery(taskId);
  const updateMutation = useDeliveryStatusUpdateMutation();

  const handleStatusUpdate = async () => {
    try {
      await updateMutation.mutateAsync({
        taskId,
        update: updateData,
      });

      setShowUpdateForm(false);
      setUpdateData({ status: currentStatus as any, location: "", notes: "" });
      refetch();
      onStatusUpdate?.(updateData.status);
    } catch (error) {
      console.error("Failed to update status:", error);
      alert("Failed to update delivery status. Please try again.");
    }
  };

  const getStatusSteps = () => {
    const allStatuses = ["assigned", "picked_up", "in_transit", "delivered"];
    return allStatuses.map((status, index) => ({
      status,
      label: formatDeliveryStatus(status),
      isCompleted: allStatuses.indexOf(currentStatus) >= index,
      isCurrent: currentStatus === status,
    }));
  };

  const nextValidStatuses = getNextValidStatus(currentStatus);

  return (
    <div className={`bg-white rounded-lg border p-6 ${className}`}>
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Delivery Status</h3>
        {isDeliveryAgent && nextValidStatuses.length > 0 && (
          <button
            onClick={() => setShowUpdateForm(!showUpdateForm)}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm"
          >
            Update Status
          </button>
        )}
      </div>

      {/* Status Progress Bar */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          {getStatusSteps().map((step, index) => (
            <div
              key={step.status}
              className="flex flex-col items-center flex-1"
            >
              {/* Status Circle */}
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step.isCompleted
                    ? "bg-green-500 text-white"
                    : step.isCurrent
                      ? "bg-blue-500 text-white"
                      : "bg-gray-200 text-gray-500"
                }`}
              >
                {step.isCompleted ? "✓" : index + 1}
              </div>

              {/* Status Label */}
              <div className="mt-2 text-xs text-center">
                <div
                  className={`font-medium ${
                    step.isCurrent ? "text-blue-600" : "text-gray-600"
                  }`}
                >
                  {step.label}
                </div>
              </div>

              {/* Connecting Line */}
              {index < getStatusSteps().length - 1 && (
                <div
                  className={`absolute h-0.5 w-full top-4 left-1/2 transform -translate-y-1/2 ${
                    step.isCompleted ? "bg-green-500" : "bg-gray-200"
                  }`}
                  style={{
                    left: `${((index + 1) / getStatusSteps().length) * 100}%`,
                    width: `${100 / getStatusSteps().length}%`,
                  }}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Current Status Badge */}
      <div className="mb-4">
        <span
          className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getDeliveryStatusColor(
            currentStatus
          )}`}
        >
          Current Status: {formatDeliveryStatus(currentStatus)}
        </span>
      </div>

      {/* Status Update Form */}
      {showUpdateForm && isDeliveryAgent && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-3">
            Update Delivery Status
          </h4>

          <div className="space-y-3">
            {/* Status Selection */}
            <div>
              <label className="block text-xs font-medium text-gray-600 mb-1">
                New Status
              </label>
              <select
                value={updateData.status}
                onChange={(e) =>
                  setUpdateData({
                    ...updateData,
                    status: e.target.value as any,
                  })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value={currentStatus}>
                  {formatDeliveryStatus(currentStatus)}
                </option>
                {nextValidStatuses.map((status) => (
                  <option key={status} value={status}>
                    {formatDeliveryStatus(status)}
                  </option>
                ))}
              </select>
            </div>

            {/* Location */}
            <div>
              <label className="block text-xs font-medium text-gray-600 mb-1">
                Current Location (Optional)
              </label>
              <input
                type="text"
                value={updateData.location}
                onChange={(e) =>
                  setUpdateData({ ...updateData, location: e.target.value })
                }
                placeholder="Enter current location..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              />
            </div>

            {/* Notes */}
            <div>
              <label className="block text-xs font-medium text-gray-600 mb-1">
                Notes (Optional)
              </label>
              <textarea
                value={updateData.notes}
                onChange={(e) =>
                  setUpdateData({ ...updateData, notes: e.target.value })
                }
                placeholder="Add any additional notes..."
                rows={2}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              />
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-2">
              <button
                onClick={handleStatusUpdate}
                disabled={
                  updateMutation.isPending ||
                  !canUpdateStatus(currentStatus, updateData.status)
                }
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:bg-gray-400 text-sm"
              >
                {updateMutation.isPending ? "Updating..." : "Update Status"}
              </button>
              <button
                onClick={() => setShowUpdateForm(false)}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 text-sm"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Status History */}
      <div>
        <h4 className="text-sm font-medium text-gray-700 mb-3">
          Status History
        </h4>

        {isLoading ? (
          <div className="text-sm text-gray-500">Loading status history...</div>
        ) : statusHistory && statusHistory.length > 0 ? (
          <div className="space-y-3">
            {statusHistory.map((entry: any, index: number) => (
              <div
                key={index}
                className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg"
              >
                <div
                  className={`w-3 h-3 rounded-full mt-1 ${
                    getDeliveryStatusColor(entry.status).split(" ")[1]
                  }`}
                />
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900">
                      {formatDeliveryStatus(entry.status)}
                    </span>
                    <span className="text-xs text-gray-500">
                      {new Date(entry.timestamp).toLocaleString()}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    {entry.message}
                  </div>
                  {entry.location && (
                    <div className="text-xs text-gray-500 mt-1">
                      📍 {entry.location}
                    </div>
                  )}
                  <div className="text-xs text-gray-400 mt-1">
                    Updated by: {entry.loggedBy}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-sm text-gray-500">
            No status history available.
          </div>
        )}
      </div>

      {/* Error Display */}
      {updateMutation.error && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-600 text-sm">{updateMutation.error.message}</p>
        </div>
      )}
    </div>
  );
};
