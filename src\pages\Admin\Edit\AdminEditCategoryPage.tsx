import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { useParams, useNavigate } from "react-router-dom";
import { AdminWrapper } from "../../../components/AdminWrapper";
import { InteractiveButton } from "../../../components/InteractiveButton";
import { MkdInputV2 } from "../../../components/MkdInputV2";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import {
  useCategoryQuery,
  useUpdateCategoryMutation,
} from "../../../query/useCategory";
import { Skeleton } from "../../../components/Skeleton";

const schema = yup.object({
  name: yup.string().required("Category name is required"),
  status: yup.string().required("Status is required"),
});

interface FormValues {
  name: string;
  status: string;
}

const AdminEditCategoryPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = useForm<FormValues>({
    resolver: yupResolver(schema),
  });

  const { data: category, isLoading, error } = useCategoryQuery(id!);
  const { mutate: updateCategory, isPending: isUpdating } =
    useUpdateCategoryMutation();

  useEffect(() => {
    if (category && !category.error) {
      reset({
        name: category.data?.name || "",
        status: category.data?.status || "active",
      });
    }
  }, [category, reset]);

  const onSubmit = (data: FormValues) => {
    updateCategory(
      { id: id!, data },
      {
        onSuccess: () => {
          navigate("/admin/categories");
        },
      }
    );
  };

  if (isLoading) {
    return (
      <AdminWrapper>
        <div className="p-6 bg-[#F8F9FB] min-h-screen">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-[#111827]">Edit Category</h1>
          </div>
          <div className="bg-white p-8 rounded-lg shadow-md max-w-lg">
            <div className="space-y-6">
              <div>
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div>
                <Skeleton className="h-4 w-16 mb-2" />
                <Skeleton className="h-10 w-full" />
              </div>
              <Skeleton className="h-12 w-full" />
            </div>
          </div>
        </div>
      </AdminWrapper>
    );
  }

  if (error || (category && category.error)) {
    return (
      <AdminWrapper>
        <div className="p-6 bg-[#F8F9FB] min-h-screen">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-[#111827]">Edit Category</h1>
          </div>
          <div className="bg-white p-8 rounded-lg shadow-md max-w-lg">
            <div className="text-center py-10 text-red-500">
              {category?.message || "Failed to load category"}
            </div>
          </div>
        </div>
      </AdminWrapper>
    );
  }

  return (
    <AdminWrapper>
      <div className="p-6 bg-[#F8F9FB] min-h-screen">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-[#111827]">Edit Category</h1>
          <img
            src="https://randomuser.me/api/portraits/women/68.jpg"
            alt="User"
            className="w-10 h-10 rounded-full"
          />
        </div>

        <div className="bg-white p-8 rounded-lg shadow-md max-w-lg">
          <form onSubmit={handleSubmit(onSubmit)}>
            <MkdInputV2
              register={register}
              errors={errors}
              name="name"
              placeholder="Enter name here"
              className="w-full mb-6"
            >
              <MkdInputV2.Label className="font-semibold text-gray-700 mb-2 block">
                Category Name
              </MkdInputV2.Label>
              <MkdInputV2.Field className="!py-2.5 !px-4 !border !border-[#D1D5DB] !rounded-md w-full !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59]" />
              <MkdInputV2.Error className="text-red-500 text-sm mt-1" />
            </MkdInputV2>

            <div className="mb-6">
              <label className="font-semibold text-gray-700 mb-2 block">
                Status
              </label>
              <select
                {...register("status")}
                className="py-2.5 px-4 !border !border-[#D1D5DB] !rounded-md w-full !bg-white !text-black !placeholder-[#ADAEBC] !focus:ring-[#0F2C59] focus:outline-none focus:ring-2 focus:border-transparent"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
              {errors.status && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.status.message}
                </p>
              )}
            </div>

            <div className="flex gap-4">
              <InteractiveButton
                type="button"
                onClick={() => navigate("/admin/categories")}
                className="!bg-gray-500 !hover:bg-gray-600 !text-white !font-semibold !py-3 !rounded-md !flex-1"
              >
                Cancel
              </InteractiveButton>
              <InteractiveButton
                type="submit"
                className="!bg-[#1E293B] !hover:bg-gray-900 !text-white !font-semibold !py-3 !rounded-md !flex-1"
                loading={isUpdating}
              >
                Update Category
              </InteractiveButton>
            </div>
          </form>
        </div>
      </div>
    </AdminWrapper>
  );
};

export default AdminEditCategoryPage;
